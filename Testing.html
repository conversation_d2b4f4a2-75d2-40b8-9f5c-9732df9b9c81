<!DOCTYPE html>
<html lang="en" class="loading">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentio - Light Theme</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Lordicon Script for Animated Icons -->
    <script src="https://cdn.lordicon.com/lordicon.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    borderRadius: {
                        '4xl': '2rem',
                    },
                    colors: {
                        'brand': '#3674B5',
                        'brand-blue': '#3674B5'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'dm-sans': ['DM Sans', 'sans-serif'],
                        'dm-sans-medium': ['DM Sans', 'sans-serif'],
                        'dm-sans-extrabold': ['DM Sans', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fade-in 0.6s ease-out',
                        'slide-up': 'slide-up 0.8s ease-out',
                        'icon-hover': 'icon-hover 0.3s ease-out'
                    },
                    keyframes: {
                        'fade-in': {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        'slide-up': {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        'icon-hover': {
                            '0%': { transform: 'scale(1)' },
                            '100%': { transform: 'scale(1.1)' }
                        }
                    }
                }
            }
        }
    </script>
    <!-- Lordicon -->
    <script src="https://cdn.lordicon.com/lordicon.js"></script>
    <!-- Dotlottie Player -->
    <script src="https://unpkg.com/@dotlottie/player-component@2.7.12/dist/dotlottie-player.mjs" type="module"></script>
    <!-- Google Fonts: Inter + DM Sans -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&family=DM+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <!-- Heroicons via CDN (for backup icons) -->
    <!-- Lineicons -->
    <link href="https://cdn.lineicons.com/4.0/lineicons.css" rel="stylesheet" />
    <!-- External CSS -->
    <link rel="stylesheet" href="styles.css">

    <!-- Google Gemini AI SDK -->
    <script type="module">
        // Import the Google GenAI SDK
        import { GoogleGenAI } from 'https://esm.run/@google/genai';

        // Initialize with your API key
        const genAI = new GoogleGenAI({
            apiKey: 'AIzaSyBwFmbxQOLyDyMzw_-rJ1qYfyvO2dQ_OvI'
        });

        // Store globally for use in other scripts
        window.genAI = genAI;
        window.GoogleGenAI = GoogleGenAI;
    </script>

    <!-- Prevent auto-scrolling script -->
    <script>
        // Immediately prevent any auto-scrolling on page load
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.pathname);
        }
        // Ensure page starts at top
        window.scrollTo(0, 0);
    </script>

</head>
<body class="bg-white text-gray-800">

    <!-- Page Header -->
    <header id="page-header" class="page-header fixed top-0 left-0 right-0 z-[100]">
        <div class="container mx-auto px-12">
            <div class="flex items-center justify-between h-16 relative">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="text-xl font-bold text-black">
                        Sentio
                    </div>
                </div>

                <!-- Centered Navigation -->
                <nav class="hidden md:flex items-center space-x-6 absolute left-1/2 transform -translate-x-1/2">
                    <a href="#hero" class="page-nav-item text-sm font-medium text-black">Home</a>

                    <!-- Shared Dropdown Container -->
                    <div class="shared-dropdown-container" id="sharedDropdownContainer">
                        <!-- Features Link -->
                        <a href="#built-for-success" class="features-link page-nav-item text-sm font-medium text-black">
                            Features
                            <svg class="dropdown-arrow inline-block w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </a>

                        <!-- Benefits Link -->
                        <a href="#platform-impact" class="benefits-link page-nav-item text-sm font-medium text-black">
                            Benefits
                            <svg class="dropdown-arrow inline-block w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </a>

                        <!-- Shared Dropdown Menu -->
                        <div class="shared-dropdown">
                            <div class="dropdown-slider">
                                <!-- Features Panel -->
                                <div class="dropdown-panel features-panel">
                                    <div class="dropdown-links">
                                        <a href="#built-for-success" class="dropdown-link">
                                            <h3>Smart Analytics</h3>
                                            <p>Track sales, monitor customer behavior, and make data-driven decisions to grow your store.</p>
                                        </a>
                                        <a href="#built-for-success" class="dropdown-link">
                                            <h3>Inventory Management</h3>
                                            <p>Balance stock levels efficiently without tying up cash in excess inventory.</p>
                                        </a>
                                        <a href="#built-for-success" class="dropdown-link">
                                            <h3>Customer Support</h3>
                                            <p>24/7 AI-powered support to help your customers and boost satisfaction rates.</p>
                                        </a>
                                        <a href="#built-for-success" class="dropdown-link">
                                            <h3>Secure Payments</h3>
                                            <p>Accept all major payment methods with enterprise-grade security.</p>
                                        </a>
                                    </div>
                                </div>

                                <!-- Benefits Panel -->
                                <div class="dropdown-panel benefits-panel">
                                    <div class="dropdown-links">
                                        <a href="#platform-impact" class="dropdown-link">
                                            <h3>Quick Setup</h3>
                                            <p>Launch your store with our guided 2-hour setup process. No technical expertise required.</p>
                                        </a>
                                        <a href="#platform-impact" class="dropdown-link">
                                            <h3>Smart Inventory</h3>
                                            <p>AI-powered automation with intelligent inventory management and forecasting.</p>
                                        </a>
                                        <a href="#platform-impact" class="dropdown-link">
                                            <h3>Predictive Analytics</h3>
                                            <p>Advanced analytics that forecast trends and provide actionable business insights.</p>
                                        </a>
                                        <a href="#platform-impact" class="dropdown-link">
                                            <h3>Customer Intelligence</h3>
                                            <p>Deep behavioral insights and comprehensive customer analysis for growth.</p>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <a href="#pricing-section" class="page-nav-item text-sm font-medium text-black">Pricing</a>
                </nav>

                <!-- CTA Button -->
                <div class="hidden md:block">
                    <a href="signup.html" class="bg-brand-blue text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 text-sm inline-block">
                        Sign Up
                    </a>
                </div>

                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-black p-2 relative z-50">
                        <svg id="hamburger-icon" class="w-6 h-6 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                        <svg id="close-icon" class="w-6 h-6 absolute top-2 left-2 hidden transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Dropdown Menu -->
        <div id="mobile-menu" class="mobile-menu md:hidden">
            <div class="mobile-menu-content">
                <nav class="mobile-nav">
                    <a href="#hero" class="mobile-nav-item">Home</a>

                    <!-- Mobile Features Section -->
                    <div class="mobile-dropdown-section">
                        <button class="mobile-dropdown-toggle">
                            <span>Features</span>
                            <svg class="mobile-dropdown-arrow w-4 h-4 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="mobile-dropdown-content">
                            <a href="#built-for-success" class="mobile-dropdown-item">
                                <div>
                                    <h4>Smart Analytics</h4>
                                    <p>Track sales and monitor customer behavior</p>
                                </div>
                            </a>
                            <a href="#built-for-success" class="mobile-dropdown-item">
                                <div>
                                    <h4>Inventory Management</h4>
                                    <p>Balance stock levels efficiently</p>
                                </div>
                            </a>
                            <a href="#built-for-success" class="mobile-dropdown-item">
                                <div>
                                    <h4>Customer Support</h4>
                                    <p>24/7 AI-powered support</p>
                                </div>
                            </a>
                            <a href="#built-for-success" class="mobile-dropdown-item">
                                <div>
                                    <h4>Secure Payments</h4>
                                    <p>Accept all major payment methods</p>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Mobile Benefits Section -->
                    <div class="mobile-dropdown-section">
                        <button class="mobile-dropdown-toggle">
                            <span>Benefits</span>
                            <svg class="mobile-dropdown-arrow w-4 h-4 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="mobile-dropdown-content">
                            <a href="#platform-impact" class="mobile-dropdown-item">
                                <div>
                                    <h4>Quick Setup</h4>
                                    <p>Launch your store with guided setup</p>
                                </div>
                            </a>
                            <a href="#platform-impact" class="mobile-dropdown-item">
                                <div>
                                    <h4>Smart Inventory</h4>
                                    <p>AI-powered automation and forecasting</p>
                                </div>
                            </a>
                            <a href="#platform-impact" class="mobile-dropdown-item">
                                <div>
                                    <h4>Predictive Analytics</h4>
                                    <p>Advanced analytics and insights</p>
                                </div>
                            </a>
                            <a href="#platform-impact" class="mobile-dropdown-item">
                                <div>
                                    <h4>Customer Intelligence</h4>
                                    <p>Deep behavioral insights</p>
                                </div>
                            </a>
                        </div>
                    </div>

                    <a href="#pricing-section" class="mobile-nav-item">Pricing</a>
                </nav>

                <!-- Mobile CTA Button -->
                <div class="mobile-cta">
                    <a href="signup.html" class="bg-brand-blue text-white font-medium py-3 px-6 rounded-lg w-full transition-all duration-300 transform hover:scale-105 inline-block text-center">
                        Sign Up
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <div id="hero" class="relative overflow-hidden">
        <div class="max-w-[85rem] mx-auto px-4 sm:px-6 lg:px-8 pt-32 sm:pt-40 pb-10">
            <!-- Announcement Banner -->
            <div class="flex justify-center animate-on-scroll delay-100">
                <a class="inline-flex items-center gap-x-2 bg-white border border-gray-200 text-sm text-gray-800 p-1 ps-3 rounded-full transition hover:border-gray-300 focus:outline-hidden focus:border-gray-300" href="#">
                    Introducing Sentio With <span class="text-brand">AI</span>
                    <span class="py-1.5 px-2.5 inline-flex justify-center items-center gap-x-2 rounded-full bg-gray-200 font-semibold text-sm text-gray-600">
                        <lord-icon
                            src="https://cdn.lordicon.com/ircnfpus.json"
                            trigger="hover"
                            state="hover-ternd-flat-3"
                            style="width:16px;height:16px;">
                        </lord-icon>
                    </span>
                </a>
            </div>
            <!-- End Announcement Banner -->

            <!-- Title -->
            <div class="mt-5 max-w-2xl text-center mx-auto animate-on-scroll delay-200">
                <h1 class="block font-bold text-gray-800 text-4xl md:text-5xl lg:text-6xl">
                    Launch Your Store
                    <span class="bg-clip-text bg-linear-to-tl from-brand to-gray-900 text-transparent">Grow Your Business</span>
                </h1>
            </div>
            <!-- End Title -->

            <div class="mt-5 max-w-3xl text-center mx-auto animate-on-scroll delay-300">
                <p class="text-lg text-gray-600">Intelligent inventory management and sales analytics tools that enable small businesses to easily launch, monitor, and scale their online stores for sustainable growth.</p>
            </div>

            <!-- Button -->
            <div class="mt-8 flex justify-center animate-on-scroll delay-400">
                <a class="hero-primary-btn inline-flex justify-center items-center text-sm font-medium" href="#">
                    Start Your Store Today
                </a>
            </div>
            <!-- End Button -->
        </div>
    </div>

    <!-- E-commerce Dashboard Showcase Section -->
    <div id="showcase" class="container mx-auto px-4 pb-16">
        <div class="relative max-w-6xl mx-auto animate-on-scroll slow">
            <!-- Outer border with 0.2cm spacing -->
            <div class="border border-gray-400 rounded-lg p-2">
                <div class="bg-white rounded-lg border border-gray-400 shadow-xl overflow-hidden relative">
                    <!-- Disclaimer Overlay - Inside the dashboard container -->
                    <div id="dashboardDisclaimer" class="dashboard-disclaimer-overlay">
                        <div class="disclaimer-content">
                            <div class="disclaimer-icon">
                                <lord-icon
                                    src="https://cdn.lordicon.com/yhtmwrae.json"
                                    trigger="in"
                                    delay="1200"
                                    state="in-info"
                                    colors="primary:#3674B5"
                                    style="width:32px;height:32px;">
                                </lord-icon>
                            </div>
                            <h3 class="disclaimer-title">Just a Demo</h3>
                            <p class="disclaimer-text">
                                Hey! This is just a visual demo. The real site will have actual products and working features.
                            </p>
                            <a href="#" id="disclaimerContinueBtn" class="subscribe-btn">Continue to Demo</a>
                        </div>
                    </div>

                    <!-- Confirmation Message - Inside the dashboard container -->
                    <div id="confirmationMessage" class="confirmation-message">
                        <div class="confirmation-content">
                            <lord-icon
                                src="https://cdn.lordicon.com/rxgzsafd.json"
                                trigger="click"
                                state="in-check"
                                colors="primary:#10b981"
                                style="width:24px;height:24px;margin-right:8px;">
                            </lord-icon>
                            <span>Thank you for understanding! Please feel free to explore the demo interface.</span>
                        </div>
                    </div>

                    <!-- E-commerce Interface Content -->
                    <div class="h-[600px] bg-white overflow-y-auto flex flex-col">

                        <!-- Header -->
                        <header id="main-header" class="sticky top-0 z-40 bg-gradient-to-r from-white/70 via-gray-50/60 to-white/70 backdrop-blur-xl border-b border-white/30 px-6 py-4 shadow-2xl/20 flex-shrink-0 transition-all duration-300 ease-in-out backdrop-blur-enhanced">
                            <div class="flex items-center justify-between">
                                <!-- Logo -->
                                <div class="logo text-2xl font-bold text-black transition-all duration-300 font-dm-sans-extrabold">
                                    Sentio
                                </div>

                                <!-- Navigation -->
                                <nav class="hidden md:flex space-x-8">
                                    <a href="#" class="nav-item text-black font-medium transition-all duration-300 font-dm-sans-medium">Home</a>
                                    <a href="#" class="nav-item text-black font-medium transition-all duration-300 font-dm-sans-medium">Products</a>
                                    <a href="#" class="nav-item text-black font-medium transition-all duration-300 font-dm-sans-medium">Categories</a>
                                    <a href="#" class="nav-item text-black font-medium transition-all duration-300 font-dm-sans-medium">About</a>
                                    <a href="#" class="nav-item text-black font-medium transition-all duration-300 font-dm-sans-medium">Contact</a>
                                </nav>

                                <!-- Search & Icons -->
                                <div class="flex items-center space-x-4">
                                    <!-- Search -->
                                    <div class="search-container relative">
                                        <input type="text" placeholder="Search products..."
                                               class="w-64 px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-300">
                                        <svg class="search-icon absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>

                                    <!-- Wishlist -->
                                    <div class="wishlist-heart cursor-pointer p-2">
                                        <lord-icon
                                            src="https://cdn.lordicon.com/vgwutnhw.json"
                                            trigger="hover"
                                            stroke="bold"
                                            colors="primary:#000000,secondary:#000000"
                                            style="width:24px;height:24px;">
                                        </lord-icon>
                                    </div>

                                    <!-- User Account -->
                                    <div class="relative cursor-pointer p-2">
                                        <lord-icon
                                            src="https://cdn.lordicon.com/cniwvohj.json"
                                            trigger="hover"
                                            colors="primary:#000000,secondary:#3674b5"
                                            style="width:24px;height:24px;">
                                        </lord-icon>
                                    </div>

                                    <!-- Shopping Cart -->
                                    <div class="relative cart-icon cursor-pointer p-2">
                                        <lord-icon
                                            src="https://cdn.lordicon.com/nvtfowkn.json"
                                            trigger="hover"
                                            colors="primary:#000000,secondary:#3674b5"
                                            style="width:24px;height:24px;">
                                        </lord-icon>
                                        <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center pulse">3</span>
                                    </div>
                                </div>
                            </div>
                        </header>

                        <!-- Main Content -->
                        <div class="flex flex-1">
                            <!-- Sidebar Categories -->
                            <aside class="w-64 bg-gray-50 p-6 border-r border-gray-200 gradient-overlay flex-shrink-0">
                                <h3 class="text-lg font-semibold text-black mb-4 flex items-center">
                                    <lord-icon
                                        src="https://cdn.lordicon.com/dutqakce.json"
                                        trigger="hover"
                                        colors="primary:#000000,secondary:#3674b5"
                                        style="width:20px;height:20px;margin-right:8px;">
                                    </lord-icon>
                                    Categories
                                </h3>
                                <ul class="space-y-2">
                                    <li>
                                        <a href="#" class="category-item flex items-center px-3 py-2 rounded text-black cursor-pointer">
                                            <lord-icon
                                                src="https://cdn.lordicon.com/zczzhvwa.json"
                                                trigger="hover"
                                                colors="primary:#000000,secondary:#3674b5"
                                                style="width:20px;height:20px;margin-right:12px;">
                                            </lord-icon>
                                            Electronics
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" class="category-item flex items-center px-3 py-2 rounded text-black cursor-pointer">
                                            <lord-icon
                                                src="https://cdn.lordicon.com/zmvzumis.json"
                                                trigger="morph"
                                                state="morph-shopping-bag-open"
                                                colors="primary:#000000,secondary:#3674b5"
                                                style="width:20px;height:20px;margin-right:12px;">
                                            </lord-icon>
                                            Clothing
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" class="category-item flex items-center px-3 py-2 rounded text-black cursor-pointer">
                                            <lord-icon
                                                src="https://cdn.lordicon.com/upjgggre.json"
                                                trigger="morph"
                                                state="morph-home-1"
                                                colors="primary:#000000,secondary:#3674b5"
                                                style="width:20px;height:20px;margin-right:12px;">
                                            </lord-icon>
                                            Home & Garden
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" class="category-item flex items-center px-3 py-2 rounded text-black cursor-pointer">
                                            <lord-icon
                                                src="https://cdn.lordicon.com/sbhkbqnq.json"
                                                trigger="hover"
                                                stroke="bold"
                                                colors="primary:#000000,secondary:#000000"
                                                style="width:20px;height:20px;margin-right:12px;">
                                            </lord-icon>
                                            Sports
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" class="category-item flex items-center px-3 py-2 rounded text-black cursor-pointer">
                                            <lord-icon
                                                src="https://cdn.lordicon.com/hjrbjhnq.json"
                                                trigger="morph"
                                                state="morph-book"
                                                colors="primary:#000000,secondary:#3674b5"
                                                style="width:20px;height:20px;margin-right:12px;">
                                            </lord-icon>
                                            Books
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" class="category-item flex items-center px-3 py-2 rounded text-black cursor-pointer">
                                            <lord-icon
                                                src="https://cdn.lordicon.com/gjpeglhr.json"
                                                trigger="hover"
                                                colors="primary:#000000,secondary:#000000"
                                                style="width:20px;height:20px;margin-right:12px;">
                                            </lord-icon>
                                            Beauty
                                        </a>
                                    </li>
                                </ul>
                            </aside>

                            <!-- Product Grid -->
                            <main class="flex-1 px-6 pt-6 pb-8">
                                <div class="mb-6">
                                    <h2 class="text-2xl font-bold text-black mb-2 font-dm-sans-bold">Your Store Dashboard</h2>
                                    <p class="text-gray-600 text-content">Manage products, track sales, and grow your business with ease</p>
                                </div>

                                <!-- Product Cards Grid -->
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    <!-- Product Card 1 -->
                                    <div class="product-card border border-gray-200 rounded-lg p-4 cursor-pointer group">
                                        <div class="relative">
                                            <div class="product-image bg-gray-100 h-48 rounded-lg mb-4 flex items-center justify-center">
                                                <span class="text-gray-400">🎧 Product Image</span>
                                            </div>
                                            <!-- Heart Icon -->
                                            <div class="heart-container absolute top-2 right-2 bg-white w-8 h-8 rounded-full shadow-md flex items-center justify-center">
                                                <lord-icon
                                                    src="https://cdn.lordicon.com/nvsfzbop.json"
                                                    trigger="morph"
                                                    stroke="bold"
                                                    state="morph-glitter"
                                                    colors="primary:#000000,secondary:#000000"
                                                    style="width:24px;height:24px;">
                                                </lord-icon>
                                            </div>
                                        </div>

                                        <!-- Product Info -->
                                        <div class="space-y-3">
                                            <h3 class="font-semibold text-black mb-2">Wireless Headphones</h3>

                                            <!-- Rating -->
                                            <div class="flex items-center space-x-2">
                                                <div class="star-rating">
                                                    <span class="star filled">★</span>
                                                    <span class="star filled">★</span>
                                                    <span class="star filled">★</span>
                                                    <span class="star filled">★</span>
                                                    <span class="star">★</span>
                                                </div>
                                                <span class="text-sm text-gray-500">(124 reviews)</span>
                                            </div>

                                            <p class="text-gray-600 text-sm mb-3">Premium quality sound with noise cancellation</p>

                                            <!-- Price and Actions -->
                                            <div class="flex items-center justify-between pt-2">
                                                <div class="flex flex-col">
                                                    <span class="text-xl font-bold text-black">$99.99</span>
                                                    <span class="text-sm text-gray-400 line-through">$129.99</span>
                                                </div>
                                                <button class="btn-primary ripple text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2">
                                                    <lord-icon
                                                        src="https://cdn.lordicon.com/ggirntso.json"
                                                        trigger="hover"
                                                        stroke="bold"
                                                        colors="primary:#ffffff,secondary:#ffffff"
                                                        style="width:16px;height:16px;">
                                                    </lord-icon>
                                                    <span>Add</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Product Card 2 -->
                                    <div class="product-card border border-gray-200 rounded-lg p-4 cursor-pointer group">
                                        <div class="relative">
                                            <div class="product-image bg-gray-100 h-48 rounded-lg mb-4 flex items-center justify-center">
                                                <span class="text-gray-400">⌚ Smart Watch</span>
                                            </div>
                                            <!-- Heart Icon -->
                                            <div class="heart-container absolute top-2 right-2 bg-white w-8 h-8 rounded-full shadow-md flex items-center justify-center">
                                                <lord-icon
                                                    src="https://cdn.lordicon.com/nvsfzbop.json"
                                                    trigger="morph"
                                                    stroke="bold"
                                                    state="morph-glitter"
                                                    colors="primary:#000000,secondary:#000000"
                                                    style="width:24px;height:24px;">
                                                </lord-icon>
                                            </div>
                                        </div>
                                        <div class="space-y-3">
                                            <h3 class="font-semibold text-black mb-2">Smart Watch</h3>
                                            <div class="flex items-center space-x-2">
                                                <div class="star-rating">
                                                    <span class="star filled">★</span>
                                                    <span class="star filled">★</span>
                                                    <span class="star filled">★</span>
                                                    <span class="star filled">★</span>
                                                    <span class="star filled">★</span>
                                                </div>
                                                <span class="text-sm text-gray-500">(89 reviews)</span>
                                            </div>
                                            <p class="text-gray-600 text-sm mb-3">Track your fitness and stay connected</p>
                                            <div class="flex items-center justify-between pt-2">
                                                <span class="text-xl font-bold text-black">$249.99</span>
                                                <button class="btn-primary ripple text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2">
                                                    <lord-icon
                                                        src="https://cdn.lordicon.com/ggirntso.json"
                                                        trigger="hover"
                                                        stroke="bold"
                                                        colors="primary:#ffffff,secondary:#ffffff"
                                                        style="width:16px;height:16px;">
                                                    </lord-icon>
                                                    <span>Add</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Product Card 3 -->
                                    <div class="product-card border border-gray-200 rounded-lg p-4 cursor-pointer group">
                                        <div class="relative">
                                            <div class="product-image bg-gray-100 h-48 rounded-lg mb-4 flex items-center justify-center">
                                                <span class="text-gray-400">🎒 Laptop Backpack</span>
                                            </div>
                                            <!-- Heart Icon -->
                                            <div class="heart-container absolute top-2 right-2 bg-white w-8 h-8 rounded-full shadow-md flex items-center justify-center">
                                                <lord-icon
                                                    src="https://cdn.lordicon.com/nvsfzbop.json"
                                                    trigger="morph"
                                                    stroke="bold"
                                                    state="morph-glitter"
                                                    colors="primary:#000000,secondary:#000000"
                                                    style="width:24px;height:24px;">
                                                </lord-icon>
                                            </div>
                                        </div>
                                        <div class="space-y-3">
                                            <h3 class="font-semibold text-black mb-2">Laptop Backpack</h3>
                                            <div class="flex items-center space-x-2">
                                                <div class="star-rating">
                                                    <span class="star filled">★</span>
                                                    <span class="star filled">★</span>
                                                    <span class="star filled">★</span>
                                                    <span class="star filled">★</span>
                                                    <span class="star">★</span>
                                                </div>
                                                <span class="text-sm text-gray-500">(67 reviews)</span>
                                            </div>
                                            <p class="text-gray-600 text-sm mb-3">Durable and stylish laptop protection</p>
                                            <div class="flex items-center justify-between pt-2">
                                                <span class="text-xl font-bold text-black">$59.99</span>
                                                <button class="btn-primary ripple text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2">
                                                    <lord-icon
                                                        src="https://cdn.lordicon.com/ggirntso.json"
                                                        trigger="hover"
                                                        stroke="bold"
                                                        colors="primary:#ffffff,secondary:#ffffff"
                                                        style="width:16px;height:16px;">
                                                    </lord-icon>
                                                    <span>Add</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Product Card 4 -->
                                    <div class="product-card border border-gray-200 rounded-lg p-4 cursor-pointer group">
                                        <div class="relative">
                                            <div class="bg-gray-100 h-48 rounded-lg mb-4 flex items-center justify-center">
                                                <span class="text-gray-400">Product Image</span>
                                            </div>
                                            <!-- Heart Icon -->
                                            <div class="heart-container absolute top-2 right-2 bg-white w-8 h-8 rounded-full shadow-md flex items-center justify-center">
                                                <lord-icon
                                                    src="https://cdn.lordicon.com/nvsfzbop.json"
                                                    trigger="morph"
                                                    stroke="bold"
                                                    state="morph-glitter"
                                                    colors="primary:#000000,secondary:#000000"
                                                    style="width:24px;height:24px;">
                                                </lord-icon>
                                            </div>
                                        </div>
                                        <h3 class="font-semibold text-black mb-2">Bluetooth Speaker</h3>
                                        <p class="text-gray-600 text-sm mb-3">Portable speaker with amazing sound quality</p>
                                        <div class="flex items-center justify-between">
                                            <span class="text-xl font-bold text-black">$79.99</span>
                                            <button class="btn-primary text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2">
                                                <lord-icon
                                                    src="https://cdn.lordicon.com/ggirntso.json"
                                                    trigger="hover"
                                                    stroke="bold"
                                                    colors="primary:#ffffff,secondary:#ffffff"
                                                    style="width:16px;height:16px;">
                                                </lord-icon>
                                                <span>Add</span>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Product Card 5 -->
                                    <div class="product-card border border-gray-200 rounded-lg p-4 cursor-pointer group">
                                        <div class="relative">
                                            <div class="bg-gray-100 h-48 rounded-lg mb-4 flex items-center justify-center">
                                                <span class="text-gray-400">Product Image</span>
                                            </div>
                                            <!-- Heart Icon -->
                                            <div class="heart-container absolute top-2 right-2 bg-white w-8 h-8 rounded-full shadow-md flex items-center justify-center">
                                                <lord-icon
                                                    src="https://cdn.lordicon.com/nvsfzbop.json"
                                                    trigger="morph"
                                                    stroke="bold"
                                                    state="morph-glitter"
                                                    colors="primary:#000000,secondary:#000000"
                                                    style="width:24px;height:24px;">
                                                </lord-icon>
                                            </div>
                                        </div>
                                        <h3 class="font-semibold text-black mb-2">Gaming Mouse</h3>
                                        <p class="text-gray-600 text-sm mb-3">High precision gaming mouse with RGB lighting</p>
                                        <div class="flex items-center justify-between">
                                            <span class="text-xl font-bold text-black">$49.99</span>
                                            <button class="btn-primary text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2">
                                                <lord-icon
                                                    src="https://cdn.lordicon.com/ggirntso.json"
                                                    trigger="hover"
                                                    stroke="bold"
                                                    colors="primary:#ffffff,secondary:#ffffff"
                                                    style="width:16px;height:16px;">
                                                </lord-icon>
                                                <span>Add</span>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Product Card 6 -->
                                    <div class="product-card border border-gray-200 rounded-lg p-4 cursor-pointer group">
                                        <div class="relative">
                                            <div class="bg-gray-100 h-48 rounded-lg mb-4 flex items-center justify-center">
                                                <span class="text-gray-400">Product Image</span>
                                            </div>
                                            <!-- Heart Icon -->
                                            <div class="heart-container absolute top-2 right-2 bg-white w-8 h-8 rounded-full shadow-md flex items-center justify-center">
                                                <lord-icon
                                                    src="https://cdn.lordicon.com/nvsfzbop.json"
                                                    trigger="morph"
                                                    stroke="bold"
                                                    state="morph-glitter"
                                                    colors="primary:#000000,secondary:#000000"
                                                    style="width:24px;height:24px;">
                                                </lord-icon>
                                            </div>
                                        </div>
                                        <h3 class="font-semibold text-black mb-2">USB-C Hub</h3>
                                        <p class="text-gray-600 text-sm mb-3">Multi-port hub for all your connectivity needs</p>
                                        <div class="flex items-center justify-between">
                                            <span class="text-xl font-bold text-black">$39.99</span>
                                            <button class="btn-primary text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2">
                                                <lord-icon
                                                    src="https://cdn.lordicon.com/ggirntso.json"
                                                    trigger="hover"
                                                    stroke="bold"
                                                    colors="primary:#ffffff,secondary:#ffffff"
                                                    style="width:16px;height:16px;">
                                                </lord-icon>
                                                <span>Add</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </main>
                        </div>

                        <!-- Footer -->
                        <footer class="bg-gray-50 border-t border-gray-200 px-6 py-8 mt-0">
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                                <!-- Company Info -->
                                <div>
                                    <h4 class="font-semibold text-black mb-4">Sentio</h4>
                                    <p class="text-gray-600 text-sm mb-4">Empowering small businesses with AI-driven e-commerce solutions. Build, grow, and scale your online store with confidence.</p>
                                    <div class="flex space-x-4">
                                        <a href="#" class="text-gray-400 hover:text-blue-600 transition-colors">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/></svg>
                                        </a>
                                        <a href="#" class="text-gray-400 hover:text-blue-600 transition-colors">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/></svg>
                                        </a>
                                        <a href="#" class="text-gray-400 hover:text-blue-600 transition-colors">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/></svg>
                                        </a>
                                    </div>
                                </div>

                                <!-- Quick Links -->
                                <div>
                                    <h4 class="font-semibold text-black mb-4">Quick Links</h4>
                                    <ul class="space-y-2 text-sm">
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">About Us</a></li>
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Contact</a></li>
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">FAQ</a></li>
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Shipping Info</a></li>
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Returns</a></li>
                                    </ul>
                                </div>

                                <!-- Categories -->
                                <div>
                                    <h4 class="font-semibold text-black mb-4">Categories</h4>
                                    <ul class="space-y-2 text-sm">
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Electronics</a></li>
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Clothing</a></li>
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Home & Garden</a></li>
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Sports</a></li>
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Books</a></li>
                                    </ul>
                                </div>

                                <!-- Customer Service -->
                                <div>
                                    <h4 class="font-semibold text-black mb-4">Customer Service</h4>
                                    <ul class="space-y-2 text-sm">
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Help Center</a></li>
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Track Order</a></li>
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Privacy Policy</a></li>
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Terms of Service</a></li>
                                        <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">Support</a></li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Payment Methods -->
                            <div class="border-t border-gray-200 mt-8 pt-6">
                                <div class="flex flex-col items-center space-y-4">
                                    <h4 class="font-semibold text-black">We Accept</h4>
                                    <div class="flex items-center space-x-4">
                                        <!-- Visa -->
                                        <div class="payment-icon bg-white p-2 rounded border shadow-sm">
                                            <svg class="w-8 h-5" viewBox="0 0 40 24" fill="none">
                                                <rect width="40" height="24" rx="4" fill="#1A1F71"/>
                                                <path d="M16.5 8.5L14.2 15.5H12.1L10.9 10.2C10.8 9.8 10.6 9.5 10.2 9.3C9.5 9 8.7 8.7 7.8 8.5L8 8.5H11.4C11.9 8.5 12.3 8.9 12.4 9.4L13.2 13.4L15.3 8.5H16.5Z" fill="white"/>
                                                <path d="M18.5 15.5H17.2L18.3 8.5H19.6L18.5 15.5Z" fill="white"/>
                                            </svg>
                                        </div>
                                        <!-- Mastercard -->
                                        <div class="payment-icon bg-white p-2 rounded border shadow-sm">
                                            <svg class="w-8 h-5" viewBox="0 0 40 24" fill="none">
                                                <rect width="40" height="24" rx="4" fill="white"/>
                                                <circle cx="15" cy="12" r="6" fill="#EB001B"/>
                                                <circle cx="25" cy="12" r="6" fill="#F79E1B"/>
                                                <path d="M20 7.5C21.5 8.8 22.5 10.3 22.5 12C22.5 13.7 21.5 15.2 20 16.5C18.5 15.2 17.5 13.7 17.5 12C17.5 10.3 18.5 8.8 20 7.5Z" fill="#FF5F00"/>
                                            </svg>
                                        </div>
                                        <!-- PayPal -->
                                        <div class="payment-icon bg-white p-2 rounded border shadow-sm">
                                            <svg class="w-8 h-5" viewBox="0 0 40 24" fill="none">
                                                <rect width="40" height="24" rx="4" fill="#003087"/>
                                                <path d="M12 8C12 7.4 12.4 7 13 7H17C19.2 7 21 8.8 21 11C21 13.2 19.2 15 17 15H15L14 17H12L12 8Z" fill="#009CDE"/>
                                                <path d="M16 10C16 9.4 16.4 9 17 9H19C20.1 9 21 9.9 21 11C21 12.1 20.1 13 19 13H18L17.5 14H16L16 10Z" fill="#012169"/>
                                            </svg>
                                        </div>
                                        <!-- Apple Pay -->
                                        <div class="payment-icon bg-white p-2 rounded border shadow-sm">
                                            <svg class="w-8 h-5" viewBox="0 0 40 24" fill="none">
                                                <rect width="40" height="24" rx="4" fill="black"/>
                                                <path d="M16.5 9.5C16.2 9.2 15.8 9 15.4 9C14.6 9 14 9.6 14 10.4C14 11.2 14.6 11.8 15.4 11.8C15.8 11.8 16.2 11.6 16.5 11.3V9.5Z" fill="white"/>
                                                <path d="M18 15V9H19.5C20.3 9 21 9.7 21 10.5V13.5C21 14.3 20.3 15 19.5 15H18Z" fill="white"/>
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="text-gray-600 text-sm">&copy; 2024 Sentio. All rights reserved. | Secure payments powered by SSL encryption</p>
                                </div>
                            </div>
                        </footer>

                    </div>

                </div>
            </div>
        </div>
    </div>


    <!-- Problem Section -->
    <div id="problem" class="py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8 text-center">
            <p class="text-base font-semibold leading-7 text-brand-blue">PROBLEM</p>
            <h2 class="mt-2 text-4xl font-bold tracking-tight text-black sm:text-5xl">E-commerce success shouldn't be this challenging.</h2>
            <div class="mt-20 grid grid-cols-1 gap-y-12 sm:grid-cols-2 lg:grid-cols-3 sm:gap-x-8 lg:gap-x-16">
                <!-- Feature 1: Cart Abandonment -->
                <div class="flex flex-col items-center">
                    <div class="flex items-center justify-center h-16 w-16 rounded-full bg-blue-50 mb-6">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" transform="rotate(0 0 0)">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M2.31641 3.25C1.90219 3.25 1.56641 3.58579 1.56641 4C1.56641 4.41421 1.90219 4.75 2.31641 4.75H3.49696C3.87082 4.75 4.18759 5.02534 4.23965 5.39556L5.49371 14.3133C5.6499 15.424 6.60021 16.25 7.72179 16.25L18.0664 16.25C18.4806 16.25 18.8164 15.9142 18.8164 15.5C18.8164 15.0858 18.4806 14.75 18.0664 14.75L7.72179 14.75C7.34793 14.75 7.03116 14.4747 6.9791 14.1044L6.85901 13.2505H17.7114C18.6969 13.2505 19.5678 12.6091 19.8601 11.668L21.7824 5.48032C21.8531 5.25268 21.8114 5.00499 21.6701 4.81305C21.5287 4.62112 21.3045 4.50781 21.0662 4.50781H5.51677C5.14728 3.75572 4.37455 3.25 3.49696 3.25H2.31641ZM5.84051 6.00781L6.64807 11.7505H17.7114C18.0399 11.7505 18.3302 11.5367 18.4277 11.223L20.0478 6.00781H5.84051Z" fill="#3674B5"/>
                            <path d="M7.78418 17.75C6.81768 17.75 6.03418 18.5335 6.03418 19.5C6.03418 20.4665 6.81768 21.25 7.78418 21.25C8.75068 21.25 9.53428 20.4665 9.53428 19.5C9.53428 18.5335 8.75068 17.75 7.78418 17.75Z" fill="#3674B5"/>
                            <path d="M14.5703 19.5C14.5703 18.5335 15.3538 17.75 16.3203 17.75C17.2868 17.75 18.0704 18.5335 18.0704 19.5C18.0704 20.4665 17.2869 21.25 16.3204 21.25C15.3539 21.25 14.5703 20.4665 14.5703 19.5Z" fill="#3674B5"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold leading-7 text-black">Cart Abandonment</h3>
                    <p class="mt-2 text-base leading-7 text-black">
                        70% of shoppers add items to their cart but don't complete the purchase. It happens everywhere, across all platforms.
                    </p>
                </div>
                <!-- Feature 2: Inventory Balance -->
                <div class="flex flex-col items-center">
                    <div class="flex items-center justify-center h-16 w-16 rounded-full bg-blue-50 mb-6">
                        <svg width="32" height="32" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" transform="rotate(0 0 0)">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.91623 4.96229C6.31475 4.25385 7.0644 3.81543 7.87725 3.81543H16.1228C16.9356 3.81543 17.6852 4.25385 18.0838 4.96229L20.461 9.18826C20.6505 9.52506 20.75 9.90497 20.75 10.2914V19.0646C20.75 20.3072 19.7426 21.3146 18.5 21.3146H5.5C4.25736 21.3146 3.25 20.3072 3.25 19.0646V10.2914C3.25 9.90497 3.34952 9.52506 3.53898 9.18826L5.91623 4.96229ZM11.25 9.14853V5.31543H7.87725C7.6063 5.31543 7.35641 5.46157 7.22357 5.69772L5.28238 9.14853H11.25ZM4.75 10.6485V19.0646C4.75 19.4788 5.08579 19.8146 5.5 19.8146H18.5C18.9142 19.8146 19.25 19.4788 19.25 19.0646V10.6485H4.75ZM18.7176 9.14853H12.75V5.31543H16.1228C16.3937 5.31543 16.6436 5.46157 16.7764 5.69772L18.7176 9.14853Z" fill="#3674B5"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold leading-7 text-black">Inventory Balance</h3>
                    <p class="mt-2 text-base leading-7 text-black">
                        Balancing stock levels without tying up cash is retail's daily challenge. Every growing business experiences this reality.
                    </p>
                </div>
                <!-- Feature 3: Marketing Costs -->
                <div class="flex flex-col items-center">
                    <div class="flex items-center justify-center h-16 w-16 rounded-full bg-blue-50 mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="32" height="32" color="#3674B5" fill="none">
                            <path d="M14.4998 11C14.4998 12.3807 13.3805 13.5 11.9998 13.5C10.6191 13.5 9.49982 12.3807 9.49982 11C9.49982 9.61929 10.6191 8.5 11.9998 8.5C13.3805 8.5 14.4998 9.61929 14.4998 11Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M22 13V5.92705C22 5.35889 21.6756 4.84452 21.1329 4.67632C20.1903 4.38421 18.4794 4 16 4C11.4209 4 10.1967 5.67747 3.87798 4.42361C2.92079 4.23366 2 4.94531 2 5.92116V15.9382C2 16.6265 2.47265 17.231 3.1448 17.3792C8.39034 18.536 10.3316 17.7972 13 17.362" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M2 8C3.95133 8 5.70483 6.40507 5.92901 4.75417M18.5005 4.5C18.5005 6.53964 20.2655 8.46899 22 8.46899M6.00049 17.4961C6.00049 15.287 4.20963 13.4961 2.00049 13.4961" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M19 14V20M16 17H22" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold leading-7 text-black">Marketing Costs</h3>
                    <p class="mt-2 text-base leading-7 text-black">
                        Digital advertising costs have risen 60% in recent years across platforms. Businesses now spend more to reach audiences.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bento Grid Features Section -->
    <div class="bg-gray-50 py-24 sm:py-32" id="built-for-success">
      <div class="mx-auto max-w-2xl px-6 lg:max-w-7xl lg:px-8">
        <h2 class="text-center text-base/7 font-semibold text-brand-blue">BUILT FOR SUCCESS</h2>
        <p class="mx-auto mt-2 max-w-lg text-center text-4xl font-semibold tracking-tight text-balance text-gray-950 sm:text-5xl">Everything you need to grow your business</p>
        <div class="mt-10 grid gap-4 sm:mt-16 lg:grid-cols-3 lg:grid-rows-2 bento-grid">
          <div class="relative lg:row-span-2 bento-card">
            <div class="absolute inset-px rounded-lg bg-white lg:rounded-l-4xl"></div>
            <div class="relative flex h-full flex-col overflow-hidden rounded-[calc(var(--radius-lg)+1px)] lg:rounded-l-[calc(2rem+1px)]">
              <div class="px-8 pt-8 pb-3 sm:px-10 sm:pt-10 sm:pb-0">
                <p class="mt-2 text-lg font-medium tracking-tight text-gray-950 max-lg:text-center">Smart Analytics</p>
                <p class="mt-2 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">Track sales, monitor customer behavior, and make data-driven decisions to grow your store.</p>
              </div>
              <div class="@container relative min-h-120 w-full grow max-lg:mx-auto max-lg:max-w-sm">
                <!-- Analytics Dashboard -->
                <div class="h-full flex flex-col p-4">
                  <!-- Dashboard Header -->
                  <div class="mb-6">
                    <div class="grid grid-cols-3 gap-3">
                      <!-- Revenue Card -->
                      <div class="bg-white rounded-2xl p-3 shadow-sm border border-gray-200">
                        <div class="text-xs text-gray-500 mb-1">Revenue</div>
                        <div class="text-lg font-bold text-brand-blue">$24.5K</div>
                        <div class="text-xs text-green-600">+12.5%</div>
                      </div>
                      <!-- Orders Card -->
                      <div class="bg-white rounded-2xl p-3 shadow-sm border border-gray-200">
                        <div class="text-xs text-gray-500 mb-1">Orders</div>
                        <div class="text-lg font-bold text-gray-800">1,247</div>
                        <div class="text-xs text-green-600">+8.2%</div>
                      </div>
                      <!-- Conversion Card -->
                      <div class="bg-white rounded-2xl p-3 shadow-sm border border-gray-200">
                        <div class="text-xs text-gray-500 mb-1">Conv. Rate</div>
                        <div class="text-lg font-bold text-gray-800">3.4%</div>
                        <div class="text-xs text-red-500">-0.3%</div>
                      </div>
                    </div>
                  </div>

                  <!-- Chart Area - Full Height -->
                  <div class="bg-white rounded-lg shadow-sm flex-1 border border-gray-200 relative overflow-hidden">
                    <!-- Full Height Chart Container -->
                    <div class="absolute inset-0">
                      <!-- SVG Area Chart -->
                      <svg class="w-full h-full" viewBox="0 0 280 200" preserveAspectRatio="none">
                        <!-- Gradient Definition -->
                        <defs>
                          <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" style="stop-color:#3674B5;stop-opacity:0.8"/>
                            <stop offset="100%" style="stop-color:#3674B5;stop-opacity:0.1"/>
                          </linearGradient>
                        </defs>

                        <!-- Area Fill -->
                        <path d="M0,150 Q40,140 80,130 T160,105 Q200,95 240,85 L280,70 L280,200 L0,200 Z"
                              fill="url(#areaGradient)" />

                        <!-- Line -->
                        <path d="M0,150 Q40,140 80,130 T160,105 Q200,95 240,85 L280,70"
                              stroke="#3674B5"
                              stroke-width="2"
                              fill="none" />

                        <!-- Data Point Dot and Line -->
                        <circle cx="140" cy="105" r="4" fill="#3674B5" stroke="#ffffff" stroke-width="2">
                          <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite"/>
                          <animate attributeName="opacity" values="1;0.7;1" dur="2s" repeatCount="indefinite"/>
                        </circle>

                        <!-- Continuous Ripple Wave Animation -->
                        <circle cx="140" cy="105" r="0" fill="none" stroke="#3674B5" stroke-width="1" opacity="0">
                          <animate attributeName="r" values="0;15;25" dur="2s" repeatCount="indefinite"/>
                          <animate attributeName="opacity" values="0.6;0.3;0" dur="2s" repeatCount="indefinite"/>
                          <animate attributeName="stroke-width" values="1.5;1;0.5" dur="2s" repeatCount="indefinite"/>
                        </circle>

                        <!-- Second Continuous Ripple (Delayed) -->
                        <circle cx="140" cy="105" r="0" fill="none" stroke="#3674B5" stroke-width="1" opacity="0">
                          <animate attributeName="r" values="0;15;25" dur="2s" begin="0.7s" repeatCount="indefinite"/>
                          <animate attributeName="opacity" values="0.6;0.3;0" dur="2s" begin="0.7s" repeatCount="indefinite"/>
                          <animate attributeName="stroke-width" values="1.5;1;0.5" dur="2s" begin="0.7s" repeatCount="indefinite"/>
                        </circle>

                        <!-- Third Continuous Ripple (More Delayed) -->
                        <circle cx="140" cy="105" r="0" fill="none" stroke="#3674B5" stroke-width="1" opacity="0">
                          <animate attributeName="r" values="0;15;25" dur="2s" begin="1.3s" repeatCount="indefinite"/>
                          <animate attributeName="opacity" values="0.6;0.3;0" dur="2s" begin="1.3s" repeatCount="indefinite"/>
                          <animate attributeName="stroke-width" values="1.5;1;0.5" dur="2s" begin="1.3s" repeatCount="indefinite"/>
                        </circle>

                        <line x1="140" y1="105" x2="140" y2="200" stroke="#3674B5" stroke-width="1" opacity="0.5"/>
                      </svg>

                      <!-- Overlaid Title -->
                      <div class="absolute top-4 left-4 text-xs text-gray-500 bg-white bg-opacity-90 px-2 py-1 rounded">
                        Weekly Sales Trend
                      </div>

                      <!-- Tooltip in Center with Outline -->
                      <div class="absolute top-1/3 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg" style="border: 1px solid rgba(255,255,255,0.2); font-size: 10px;">
                        <span id="animatedNumber" style="
                          font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
                          font-weight: normal;
                          font-variant-numeric: tabular-nums;
                          letter-spacing: 0.3px;
                          -webkit-font-smoothing: antialiased;
                          -moz-osx-font-smoothing: grayscale;
                          text-rendering: optimizeLegibility;
                          font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1, 'pnum' 1, 'tnum' 1;
                          font-synthesis: none;
                          text-shadow: 0 0 1px rgba(255,255,255,0.1);
                          backface-visibility: hidden;
                          transform: translateZ(0);
                          will-change: contents;
                        ">2,582</span>
                      </div>
                    </div>
                  </div>

                  <!-- Bottom Metrics -->
                  <div class="grid grid-cols-2 gap-3 mt-6">
                    <div class="bg-white rounded-2xl p-3 shadow-sm border border-gray-200">
                      <div class="text-xs text-gray-500 mb-1">Top Product</div>
                      <div class="text-sm font-semibold text-gray-800">Wireless Headphones</div>
                    </div>
                    <div class="bg-white rounded-2xl p-3 shadow-sm border border-gray-200">
                      <div class="text-xs text-gray-500 mb-1">Avg. Order</div>
                      <div class="text-sm font-semibold text-brand-blue">$89.50</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="pointer-events-none absolute inset-px rounded-lg shadow-sm outline outline-black/5 lg:rounded-l-4xl"></div>
          </div>
          <div class="relative max-lg:row-start-1 bento-card">
            <div class="absolute inset-px rounded-lg bg-white max-lg:rounded-t-4xl"></div>
            <div class="relative flex h-full flex-col overflow-hidden rounded-[calc(var(--radius-lg)+1px)] max-lg:rounded-t-[calc(2rem+1px)]">
              <div class="px-8 pt-8 sm:px-10 sm:pt-10">
                <p class="mt-2 text-lg font-medium tracking-tight text-gray-950 max-lg:text-center">Inventory Management</p>
                <p class="mt-2 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">Balance stock levels efficiently without tying up cash in excess inventory.</p>
              </div>
              <div class="flex flex-1 items-center justify-center px-8 max-lg:pt-10 max-lg:pb-12 sm:px-10 lg:pb-2">
                <!-- Animated Inventory Dashboard -->
                <div class="w-full max-lg:max-w-xs space-y-3">
                  <!-- Stock Level Indicators -->
                  <div class="grid grid-cols-3 gap-2">
                    <!-- Electronics Stock -->
                    <div class="inventory-card bg-white rounded-lg p-2 border border-gray-200 shadow-sm">
                      <div class="flex items-center justify-between mb-1">
                        <span class="text-xs text-gray-500">Electronics</span>
                        <div class="w-1.5 h-1.5 rounded-full bg-green-500 inventory-pulse-dot"></div>
                      </div>
                      <div class="mb-1">
                        <div class="bg-gray-200 rounded-full h-1.5 overflow-hidden">
                          <div class="inventory-progress-fill electronics h-full rounded-full"></div>
                        </div>
                      </div>
                      <div class="inventory-counter text-xs font-medium text-gray-800" data-target="156">0</div>
                    </div>

                    <!-- Clothing Stock -->
                    <div class="inventory-card bg-white rounded-lg p-2 border border-gray-200 shadow-sm">
                      <div class="flex items-center justify-between mb-1">
                        <span class="text-xs text-gray-500">Clothing</span>
                        <div class="w-1.5 h-1.5 rounded-full bg-yellow-500 inventory-pulse-dot"></div>
                      </div>
                      <div class="mb-1">
                        <div class="bg-gray-200 rounded-full h-1.5 overflow-hidden">
                          <div class="inventory-progress-fill clothing h-full rounded-full"></div>
                        </div>
                      </div>
                      <div class="inventory-counter text-xs font-medium text-gray-800" data-target="67">0</div>
                    </div>

                    <!-- Home Stock -->
                    <div class="inventory-card bg-white rounded-lg p-2 border border-gray-200 shadow-sm">
                      <div class="flex items-center justify-between mb-1">
                        <span class="text-xs text-gray-500">Home</span>
                        <div class="w-1.5 h-1.5 rounded-full bg-red-500 inventory-pulse-dot"></div>
                      </div>
                      <div class="mb-1">
                        <div class="bg-gray-200 rounded-full h-1.5 overflow-hidden">
                          <div class="inventory-progress-fill home h-full rounded-full"></div>
                        </div>
                      </div>
                      <div class="inventory-counter text-xs font-medium text-gray-800" data-target="23">0</div>
                    </div>
                  </div>

                  <!-- Quick Summary -->
                  <div class="flex items-center justify-between bg-white rounded-lg p-2 border border-gray-200">
                    <div class="flex items-center space-x-2">
                      <div class="w-1 h-1 rounded-full bg-red-500 animate-pulse"></div>
                      <span class="text-xs text-red-700 font-medium">
                        <span class="text-animate-container" id="lowStockText">2 Low Stock</span>
                      </span>
                    </div>
                    <div class="text-xs font-medium text-brand-blue">
                      <span class="inventory-counter" data-target="246">0</span> Total
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="pointer-events-none absolute inset-px rounded-lg shadow-sm outline outline-black/5 max-lg:rounded-t-4xl"></div>
          </div>
          <div class="relative max-lg:row-start-3 lg:col-start-2 lg:row-start-2 bento-card">
            <div class="absolute inset-px rounded-lg bg-white"></div>
            <div class="relative flex h-full flex-col overflow-hidden rounded-[calc(var(--radius-lg)+1px)]">
              <div class="px-8 pt-4 pb-2 sm:px-10 sm:pt-6 sm:pb-3">
                <p class="text-lg font-medium tracking-tight text-gray-950 max-lg:text-center">Customer Support</p>
                <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">24/7 AI-powered support to help your customers and boost satisfaction rates.</p>
              </div>
              <div class="@container flex flex-1 items-stretch max-lg:py-6 lg:pb-2 px-8 sm:px-10">
                <!-- AI Chat Interface -->
                <div class="w-full h-[min(152px,40cqw)] bg-gray-50/50 rounded-lg border border-gray-200 shadow-sm relative overflow-hidden min-w-[200px] flex flex-col">
                  <!-- Chat Header -->
                  <div class="flex items-center justify-between px-2 py-1.5 border-b border-white/20 chat-header">
                    <div class="flex items-center space-x-2">
                      <div class="w-5 h-5 rounded-full ai-avatar gradient-ai-avatar">
                        <svg class="w-2.5 h-2.5 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"/>
                        </svg>
                      </div>
                      <div>
                        <div class="text-[11px] font-medium text-gray-800">AI Support</div>
                        <div class="text-[9px] text-green-600 flex items-center">
                          <div class="w-1 h-1 bg-green-500 rounded-full mr-1"></div>
                          Online
                        </div>
                      </div>
                    </div>
                    <div class="text-[9px] text-gray-500">24/7</div>
                  </div>

                  <!-- Chat Messages Container -->
                  <div id="chatContainer" class="flex-1 overflow-y-auto p-2 space-y-2 chat-container">
                    <!-- Messages will be dynamically added here -->
                  </div>

                  <!-- Thinking Animation Container -->
                  <div id="thinkingContainer" class="px-2 pb-2" style="display: none;">
                    <div class="flex items-center space-x-2 bg-gray-50 rounded-lg p-2">
                      <div class="w-4 h-4 rounded-full" style="background: linear-gradient(135deg, #3674B5 0%, #4A90E2 100%);"></div>
                      <div class="flex flex-col">
                        <div id="thinkingStage" class="text-[10px] font-medium" style="color: #3674B5;">Processing...</div>
                        <div class="flex space-x-1 mt-1">
                          <div class="w-1 h-1 bg-gray-400 rounded-full thinking-dots"></div>
                          <div class="w-1 h-1 bg-gray-400 rounded-full thinking-dots" style="animation-delay: 0.2s;"></div>
                          <div class="w-1 h-1 bg-gray-400 rounded-full thinking-dots" style="animation-delay: 0.4s;"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Typing Indicator -->
                  <div id="typingIndicator" class="px-2 pb-2" style="display: none;">
                    <div class="flex items-center space-x-2">
                      <div class="w-4 h-4 rounded-full" style="background: linear-gradient(135deg, #3674B5 0%, #4A90E2 100%);"></div>
                      <div class="bg-gray-100 rounded-lg px-2 py-1">
                        <div class="flex space-x-1">
                          <div class="w-1 h-1 bg-gray-400 rounded-full thinking-dots"></div>
                          <div class="w-1 h-1 bg-gray-400 rounded-full thinking-dots" style="animation-delay: 0.2s;"></div>
                          <div class="w-1 h-1 bg-gray-400 rounded-full thinking-dots" style="animation-delay: 0.4s;"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="pointer-events-none absolute inset-px rounded-lg shadow-sm outline outline-black/5"></div>
          </div>
          <div class="relative lg:row-span-2 bento-card">
            <div class="absolute inset-px rounded-lg bg-white max-lg:rounded-b-4xl lg:rounded-r-4xl"></div>
            <div class="relative flex h-full flex-col overflow-hidden rounded-[calc(var(--radius-lg)+1px)] max-lg:rounded-b-[calc(2rem+1px)] lg:rounded-r-[calc(2rem+1px)] transition-all duration-300">
              <div class="px-8 pt-8 pb-8 sm:px-10 sm:pt-10 sm:pb-10 h-full flex flex-col overflow-hidden">
                <!-- Main Icon with Background -->
                <div class="flex items-center justify-center mb-3">
                  <div class="icon-container p-3 bg-blue-50 rounded-lg border border-gray-300">
                    <svg class="w-8 h-8" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg" transform="rotate(0 0 0)">
                      <path d="M12.2519 5.5C12.6662 5.5 13.0019 5.83579 13.0019 6.25V6.68778C13.9927 6.83842 14.7519 7.69397 14.7519 8.72685C14.7519 9.14106 14.4162 9.47685 14.0019 9.47685C13.5877 9.47685 13.2519 9.14106 13.2519 8.72685C13.2519 8.41613 13.0001 8.16425 12.6893 8.16425H12.0019C11.5877 8.16425 11.2519 8.50003 11.2519 8.91425V9.17932C11.2519 9.49195 11.4459 9.77178 11.7386 9.88156L13.292 10.4641C14.1702 10.7935 14.7519 11.633 14.7519 12.5709V12.8359C14.7519 13.9067 14.0039 14.8028 13.0019 15.0302V15.5C13.0019 15.9142 12.6662 16.25 12.2519 16.25C11.8377 16.25 11.5019 15.9142 11.5019 15.5V15.0624C10.5111 14.9118 9.75194 14.0562 9.75194 13.0233C9.75194 12.6091 10.0877 12.2733 10.5019 12.2733C10.9162 12.2733 11.2519 12.6091 11.2519 13.0233C11.2519 13.3341 11.5038 13.5859 11.8145 13.5859H12.5019C12.9162 13.5859 13.2519 13.2502 13.2519 12.8359V12.5709C13.2519 12.2582 13.058 11.9784 12.7653 11.8686L11.2119 11.286C10.3337 10.9567 9.75194 10.1172 9.75194 9.17932V8.91425C9.75194 7.84345 10.4999 6.94737 11.5019 6.72V6.25C11.5019 5.83579 11.8377 5.5 12.2519 5.5Z" fill="#3674B5"/>
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M13.1871 2.35323C12.5928 2.08148 11.9098 2.08148 11.3156 2.35323L5.5916 4.97088C4.79089 5.33705 4.27733 6.13663 4.27734 7.01709L4.27742 12.4822C4.27744 14.0385 4.75474 15.5847 5.75814 16.8262C6.55842 17.8164 7.64456 19.0736 8.75563 20.0899C9.31059 20.5975 9.88978 21.0618 10.4576 21.4032C11.011 21.7359 11.6317 22 12.2514 22C12.8712 22 13.4919 21.7359 14.0452 21.4032C14.6131 21.0618 15.1923 20.5976 15.7472 20.09C16.8583 19.0737 17.9444 17.8165 18.7447 16.8263C19.7481 15.5847 20.2254 14.0385 20.2254 12.4821L20.2253 7.01704C20.2253 6.1366 19.7118 5.33705 18.9111 4.97089L13.1871 2.35323ZM11.9394 3.71735C12.1375 3.62677 12.3652 3.62677 12.5632 3.71735L18.2872 6.33501C18.5541 6.45707 18.7253 6.72358 18.7253 7.01706L18.7254 12.4821C18.7254 13.7364 18.3413 14.939 17.5781 15.8834C16.7961 16.8509 15.7645 18.0413 14.7348 18.9832C14.2194 19.4546 13.7224 19.8471 13.2724 20.1177C12.8079 20.3969 12.469 20.5 12.2514 20.5C12.0339 20.5 11.695 20.3969 11.2305 20.1176C10.7805 19.8471 10.2835 19.4546 9.76804 18.9831C8.73831 18.0412 7.70673 16.8509 6.92475 15.8833C6.16153 14.939 5.77744 13.7364 5.77742 12.4822L5.77734 7.01707C5.77734 6.72358 5.94852 6.45706 6.21543 6.335L11.9394 3.71735Z" fill="#3674B5"/>
                    </svg>
                  </div>
                </div>

                <h3 class="text-lg font-medium tracking-tight text-gray-950 text-center mb-1">Secure Payments</h3>
                <p class="text-sm text-gray-600 text-center mb-4">Accept all major payment methods with enterprise-grade security and fraud protection.</p>

                <!-- Supported Payment Methods Section -->
                <div class="mb-4">
                  <h4 class="text-xs font-medium text-gray-700 text-center mb-3">Supported Payment Methods</h4>
                  <div class="grid grid-cols-2 gap-3">
                    <!-- Credit Cards -->
                    <div class="flex flex-col items-center text-center">
                      <div class="mb-1 flex items-center justify-center p-2 bg-gray-50 rounded-lg border border-gray-300">
                        <lord-icon
                            src="https://cdn.lordicon.com/vmztfafm.json"
                            trigger="morph"
                            state="morph-card"
                            style="width:32px;height:32px;">
                        </lord-icon>
                      </div>
                      <span class="text-xs text-gray-600">Credit Cards</span>
                    </div>

                    <!-- Mobile Pay -->
                    <div class="flex flex-col items-center text-center">
                      <div class="mb-1 flex items-center justify-center p-2 bg-gray-50 rounded-lg border border-gray-300">
                        <lord-icon
                            src="https://cdn.lordicon.com/uewqxptr.json"
                            trigger="hover"
                            stroke="bold"
                            state="hover-roll"
                            colors="primary:#121331,secondary:#000000"
                            style="width:32px;height:32px;">
                        </lord-icon>
                      </div>
                      <span class="text-xs text-gray-600">Mobile Pay</span>
                    </div>

                    <!-- Bank Transfer -->
                    <div class="flex flex-col items-center text-center">
                      <div class="mb-1 flex items-center justify-center p-2 bg-gray-50 rounded-lg border border-gray-300">
                        <lord-icon
                            src="https://cdn.lordicon.com/wurouayb.json"
                            trigger="hover"
                            style="width:32px;height:32px;">
                        </lord-icon>
                      </div>
                      <span class="text-xs text-gray-600">Bank Transfer</span>
                    </div>

                    <!-- Digital Wallet -->
                    <div class="flex flex-col items-center text-center">
                      <div class="mb-1 flex items-center justify-center p-2 bg-gray-50 rounded-lg border border-gray-300">
                        <lord-icon
                            src="https://cdn.lordicon.com/ytklkgsc.json"
                            trigger="hover"
                            stroke="bold"
                            colors="primary:#121331,secondary:#000000"
                            style="width:32px;height:32px;">
                        </lord-icon>
                      </div>
                      <span class="text-xs text-gray-600">Digital Pay</span>
                    </div>
                  </div>
                </div>

                <!-- Security Features Section -->
                <div class="flex-1 min-h-0">
                  <h4 class="text-xs font-medium text-gray-700 text-center mb-3">Security Features</h4>
                  <div class="space-y-2">
                    <div class="bg-green-50 rounded-lg p-2">
                      <div class="flex items-start space-x-2">
                        <svg class="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                          <div class="text-xs font-medium text-gray-900">256-bit SSL Encryption</div>
                          <div class="text-xs text-gray-600">Bank-level security</div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-green-50 rounded-lg p-2">
                      <div class="flex items-start space-x-2">
                        <svg class="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                          <div class="text-xs font-medium text-gray-900">Real-time Fraud Detection</div>
                          <div class="text-xs text-gray-600">AI-powered monitoring</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Trust Badge -->
                <div class="mt-4 pt-4 border-t border-gray-200">
                  <div class="bg-white rounded-lg px-4 py-2.5 border border-gray-200 shadow-sm">
                    <div class="flex items-center justify-center space-x-2.5">
                      <svg class="w-3.5 h-3.5 text-brand" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                      </svg>
                      <span class="text-xs font-medium text-brand">Trusted by 10,000+ businesses</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="pointer-events-none absolute inset-px rounded-lg shadow-sm outline outline-black/5 max-lg:rounded-b-4xl lg:rounded-r-4xl"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- BEGIN: Feature Slideshow Component -->
    <div class="bg-white py-24 sm:py-32 feature-slideshow-container" id="how-it-works">
        <div class="w-full max-w-6xl mx-auto p-4 sm:p-6 lg:p-8">
            <div class="text-center mb-12 animate-on-scroll">
                <p class="text-lg font-semibold mb-4" style="color: #3674B5;">How it works</p>
                <h1 class="text-4xl md:text-5xl font-bold text-gray-800 tracking-tight">Launch & Grow Fast</h1>
            </div>

            <!-- Main container for the slideshow -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center animate-on-scroll delay-200 slow">

                <!-- Left side: Feature list with compact spacing -->
                <div id="feature-list" class="space-y-2">
                    <!-- Feature items will be dynamically generated by JavaScript here -->
                </div>

                <!-- Right side: Placeholder display -->
                <div class="image-container soft-shadow relative w-full h-64 sm:h-80 md:h-96 lg:h-full rounded-2xl border border-gray-300 bg-white flex items-center justify-center">
                     <div id="feature-placeholder" class="text-center">
                         <p class="text-gray-400 text-lg font-light tracking-wide">Data Upload Interface</p>
                     </div>
                     <div id="image-skeleton" class="absolute inset-0 bg-gray-200 animate-pulse rounded-2xl hidden"></div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Feature Slideshow Component -->

    <!-- Platform Impact Showcase Section -->
    <section class="py-16 px-4" id="platform-impact">
        <div class="container mx-auto max-w-7xl">

            <!-- Section Header -->
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="section-title text-4xl md:text-5xl text-gray-900 mb-6">
                    Platform Impact
                </h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                    Measurable results that drive real business growth.
                    See the difference Sentio makes.
                </p>
            </div>

            <!-- Key Metrics Grid -->
            <div class="mb-16">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">

                    <!-- Metric 1: Setup Time -->
                    <div class="text-center animate-on-scroll">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mx-auto mb-3">
                            <i class="lni lni-timer text-brand text-lg"></i>
                        </div>
                        <div class="counter text-4xl font-bold text-gray-900 mb-2" data-target="2">0</div>
                        <div class="text-sm text-gray-600 font-medium mb-2">Average Setup Time</div>
                        <div class="bg-blue-50 text-brand text-xs font-semibold px-3 py-1 rounded-full border border-gray-200 inline-block">
                            vs 2-3 weeks traditional
                        </div>
                    </div>

                    <!-- Metric 2: Sales Increase -->
                    <div class="text-center animate-on-scroll">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mx-auto mb-3">
                            <i class="lni lni-stats-up text-brand text-lg"></i>
                        </div>
                        <div class="counter text-4xl font-bold text-gray-900 mb-2" data-target="40">0</div>
                        <div class="text-sm text-gray-600 font-medium mb-2">Average Sales Increase</div>
                        <div class="bg-blue-50 text-brand text-xs font-semibold px-3 py-1 rounded-full border border-gray-200 inline-block">
                            within first 6 months
                        </div>
                    </div>

                    <!-- Metric 3: Time Saved -->
                    <div class="text-center animate-on-scroll">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mx-auto mb-3">
                            <i class="lni lni-checkmark-circle text-brand text-lg"></i>
                        </div>
                        <div class="counter text-4xl font-bold text-gray-900 mb-2" data-target="15">0</div>
                        <div class="text-sm text-gray-600 font-medium mb-2">Time Saved</div>
                        <div class="bg-blue-50 text-brand text-xs font-semibold px-3 py-1 rounded-full border border-gray-200 inline-block">
                            on manual tasks
                        </div>
                    </div>

                    <!-- Metric 4: Platform Uptime -->
                    <div class="text-center animate-on-scroll">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mx-auto mb-3">
                            <i class="lni lni-cloud-check text-brand text-lg"></i>
                        </div>
                        <div class="counter text-4xl font-bold text-gray-900 mb-2" data-target="99.9">0</div>
                        <div class="text-sm text-gray-600 font-medium mb-2">Platform Uptime</div>
                        <div class="bg-blue-50 text-brand text-xs font-semibold px-3 py-1 rounded-full border border-gray-200 inline-block">
                            guaranteed reliability
                        </div>
                    </div>

                </div>
            </div>

            <!-- Sentio Features Showcase -->
            <div class="animate-on-scroll">
                <div class="text-center mb-12">
                    <h3 class="section-title text-3xl text-gray-900 mb-4">
                        Why Choose Sentio?
                    </h3>
                    <p class="text-gray-600 max-w-3xl mx-auto text-lg">
                        Experience the next generation of e-commerce with our AI-powered platform designed for modern businesses
                    </p>
                </div>

                <!-- Feature Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">

                    <!-- Quick Setup -->
                    <div class="feature-card bg-white rounded-xl p-6 border border-gray-200 hover:border-brand transition-all duration-300 animate-on-scroll">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mb-4">
                            <i class="lni lni-rocket text-brand text-xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Quick Setup</h4>
                        <p class="text-gray-600 text-sm leading-relaxed mb-4">
                            Get started in hours
                        </p>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            Launch your store with our guided 2-hour setup process. No technical expertise required.
                        </p>
                        <div class="mt-4">
                            <span class="bg-blue-50 text-brand text-xs font-semibold px-3 py-1 rounded-full border border-gray-200">
                                95% faster than custom builds
                            </span>
                        </div>
                    </div>

                    <!-- Smart Inventory -->
                    <div class="feature-card bg-white rounded-xl p-6 border border-gray-200 hover:border-brand transition-all duration-300 animate-on-scroll">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mb-4">
                            <i class="lni lni-cog text-brand text-xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Smart Inventory</h4>
                        <p class="text-gray-600 text-sm leading-relaxed mb-4">
                            AI-powered automation
                        </p>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            Intelligent inventory management that predicts demand and automates restocking decisions.
                        </p>
                        <div class="mt-4">
                            <span class="bg-blue-50 text-brand text-xs font-semibold px-3 py-1 rounded-full border border-gray-200">
                                90% time savings
                            </span>
                        </div>
                    </div>

                    <!-- Predictive Analytics -->
                    <div class="feature-card bg-white rounded-xl p-6 border border-gray-200 hover:border-brand transition-all duration-300 animate-on-scroll">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mb-4">
                            <i class="lni lni-stats-up text-brand text-xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Predictive Analytics</h4>
                        <p class="text-gray-600 text-sm leading-relaxed mb-4">
                            Real-time insights
                        </p>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            Advanced analytics that forecast trends and provide actionable business insights.
                        </p>
                        <div class="mt-4">
                            <span class="bg-blue-50 text-brand text-xs font-semibold px-3 py-1 rounded-full border border-gray-200">
                                Real-time data
                            </span>
                        </div>
                    </div>

                    <!-- Customer Intelligence -->
                    <div class="feature-card bg-white rounded-xl p-6 border border-gray-200 hover:border-brand transition-all duration-300 animate-on-scroll">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mb-4">
                            <i class="lni lni-users text-brand text-xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Customer Intelligence</h4>
                        <p class="text-gray-600 text-sm leading-relaxed mb-4">
                            Deep behavioral insights
                        </p>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            Understand your customers better with comprehensive behavioral analysis and personalization.
                        </p>
                    </div>

                    <!-- Transparent Pricing -->
                    <div class="feature-card bg-white rounded-xl p-6 border border-gray-200 hover:border-brand transition-all duration-300 animate-on-scroll">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mb-4">
                            <i class="lni lni-heart text-brand text-xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Transparent Pricing</h4>
                        <p class="text-gray-600 text-sm leading-relaxed mb-4">
                            All-inclusive model
                        </p>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            Simple, predictable pricing with no hidden fees. Everything you need included from day one.
                        </p>
                    </div>

                    <!-- Enterprise Security -->
                    <div class="feature-card bg-white rounded-xl p-6 border border-gray-200 hover:border-brand transition-all duration-300 animate-on-scroll">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mb-4">
                            <i class="lni lni-shield text-brand text-xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Enterprise Security</h4>
                        <p class="text-gray-600 text-sm leading-relaxed mb-4">
                            Bank-level protection
                        </p>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            Advanced security measures protect your business and customer data with 99.9% uptime guarantee.
                        </p>
                    </div>

                </div>

                <!-- Success Metrics -->
                <div class="mt-10">
                    <div class="text-center mb-8">
                        <h5 class="text-xl font-semibold text-brand mb-3">Proven Results</h5>
                        <p class="text-gray-600 max-w-2xl mx-auto">
                            Join thousands of businesses already growing with Sentio's intelligent e-commerce platform
                        </p>
                    </div>

                    <div class="flex flex-wrap justify-center gap-8 lg:gap-12">
                        <!-- Faster Setup Metric -->
                        <div class="text-center animate-on-scroll">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mx-auto mb-3">
                                <i class="lni lni-rocket text-brand text-lg"></i>
                            </div>
                            <div class="text-4xl font-bold text-gray-900 mb-2">10x</div>
                            <div class="text-sm text-gray-600 font-medium mb-2">Faster Setup</div>
                            <div class="bg-blue-50 text-brand text-xs font-semibold px-3 py-1 rounded-full border border-gray-200 inline-block">
                                vs traditional builds
                            </div>
                        </div>

                        <!-- Time Savings Metric -->
                        <div class="text-center animate-on-scroll">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mx-auto mb-3">
                                <i class="lni lni-timer text-brand text-lg"></i>
                            </div>
                            <div class="text-4xl font-bold text-gray-900 mb-2">90%</div>
                            <div class="text-sm text-gray-600 font-medium mb-2">Time Savings</div>
                            <div class="bg-blue-50 text-brand text-xs font-semibold px-3 py-1 rounded-full border border-gray-200 inline-block">
                                on inventory tasks
                            </div>
                        </div>

                        <!-- Sales Increase Metric -->
                        <div class="text-center animate-on-scroll">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mx-auto mb-3">
                                <i class="lni lni-stats-up text-brand text-lg"></i>
                            </div>
                            <div class="text-4xl font-bold text-gray-900 mb-2">40%</div>
                            <div class="text-sm text-gray-600 font-medium mb-2">Sales Increase</div>
                            <div class="bg-blue-50 text-brand text-xs font-semibold px-3 py-1 rounded-full border border-gray-200 inline-block">
                                average growth
                            </div>
                        </div>

                        <!-- Uptime Metric -->
                        <div class="text-center animate-on-scroll">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl border border-gray-200 flex items-center justify-center mx-auto mb-3">
                                <i class="lni lni-cloud-check text-brand text-lg"></i>
                            </div>
                            <div class="text-4xl font-bold text-gray-900 mb-2">99.9%</div>
                            <div class="text-sm text-gray-600 font-medium mb-2">Uptime</div>
                            <div class="bg-blue-50 text-brand text-xs font-semibold px-3 py-1 rounded-full border border-gray-200 inline-block">
                                guaranteed SLA
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-16 px-4" id="pricing-section">
        <div class="container mx-auto max-w-7xl">

            <!-- Section Header -->
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="section-title text-4xl md:text-5xl text-gray-900 mb-6">
                    Choose Your Plan
                </h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                    Select the perfect plan for your business needs. All plans include our core features with varying levels of access and support.
                </p>
            </div>

            <!-- Pricing Cards Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 w-full justify-items-center relative">

                <!-- Basic Plan -->
                <div class="pricing-card pricing-card-basic bg-white rounded-3xl shadow-sm border border-gray-200 px-8 py-4 flex flex-col aspect-square w-full max-w-lg min-h-[480px] relative animate-slide-left delay-100">
                    <h3 class="text-sm font-semibold uppercase text-gray-500 tracking-wider text-center">Basic</h3>
                    <div class="mt-4 flex items-baseline justify-center">
                        <span class="text-5xl font-bold tracking-tight text-gray-900">$16</span>
                        <span class="ml-1 text-xl font-semibold text-gray-500">/ month</span>
                    </div>
                    <p class="mt-1 text-sm text-gray-500 text-center">billed annually</p>

                    <div class="flex-grow"></div>

                    <ul class="space-y-3 text-gray-600 mt-4">
                        <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            1 User
                        </li>
                        <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            5GB Storage
                        </li>
                        <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            Basic Support
                        </li>
                        <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            Limited API Access
                        </li>
                         <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            Standard Analytics
                        </li>
                    </ul>

                    <hr class="mt-6 mb-2 border-gray-200">

                    <a href="#" class="subscribe-btn mt-2">Subscribe</a>
                    <p class="mt-6 text-center text-xs text-gray-500">Perfect for individuals and small projects</p>
                </div>

                <!-- Pro Plan (Highlighted) -->
                <div class="pricing-card pricing-card-pro bg-white rounded-3xl shadow-xl border-2 px-8 py-4 flex flex-col relative aspect-square w-full max-w-lg min-h-[480px] animate-scale-up delay-200" style="border-color: #3674B5;">
                    <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
                        <div class="popular-badge">
                            <span>Most Popular</span>
                        </div>
                    </div>

                    <h3 class="text-sm font-semibold uppercase text-gray-500 tracking-wider text-center">Pro</h3>
                    <div class="mt-4 flex items-baseline justify-center">
                        <span class="text-5xl font-bold tracking-tight text-gray-900">$40</span>
                        <span class="ml-1 text-xl font-semibold text-gray-500">/ month</span>
                    </div>
                    <p class="mt-1 text-sm text-gray-500 text-center">billed annually</p>

                    <div class="flex-grow"></div>

                    <ul class="space-y-3 text-gray-600 mt-4">
                         <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            5 Users
                        </li>
                        <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            50GB Storage
                        </li>
                        <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            Priority Support
                        </li>
                        <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            Full API Access
                        </li>
                         <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            Advanced Analytics
                        </li>
                    </ul>

                    <hr class="mt-6 mb-2 border-gray-200">

                    <a href="#" class="button mt-2">
                        <div class="fold"></div>
                        <div class="points_wrapper">
                            <div class="point"></div>
                            <div class="point"></div>
                            <div class="point"></div>
                            <div class="point"></div>
                            <div class="point"></div>
                            <div class="point"></div>
                            <div class="point"></div>
                            <div class="point"></div>
                            <div class="point"></div>
                            <div class="point"></div>
                        </div>
                        <div class="inner">Subscribe</div>
                    </a>
                    <p class="mt-6 text-center text-xs text-gray-500">Ideal for growing businesses and teams</p>
                </div>

                <!-- Enterprise Plan -->
                <div class="pricing-card pricing-card-enterprise bg-white rounded-3xl shadow-sm border border-gray-200 px-8 py-4 flex flex-col aspect-square w-full max-w-lg min-h-[480px] relative animate-slide-right delay-300">
                    <h3 class="text-sm font-semibold uppercase text-gray-500 tracking-wider text-center">Enterprise</h3>
                    <div class="mt-4 flex items-baseline justify-center">
                        <span class="text-5xl font-bold tracking-tight text-gray-900">$82</span>
                        <span class="ml-1 text-xl font-semibold text-gray-500">/ month</span>
                    </div>
                    <p class="mt-1 text-sm text-gray-500 text-center">billed annually</p>

                    <div class="flex-grow"></div>

                    <ul class="space-y-3 text-gray-600 mt-4">
                        <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            Unlimited Users
                        </li>
                        <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            500GB Storage
                        </li>
                        <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            24/7 Premium Support
                        </li>
                        <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            Custom Integrations
                        </li>
                         <li class="flex items-center">
                            <lord-icon
                                src="https://cdn.lordicon.com/hmzvkifi.json"
                                trigger="hover"
                                stroke="bold"
                                state="hover-pinch"
                                colors="primary:#3674b5"
                                style="width:20px;height:20px;margin-right:12px;">
                            </lord-icon>
                            AI-Powered Insights
                        </li>
                    </ul>

                    <hr class="mt-6 mb-2 border-gray-200">

                    <a href="#" class="subscribe-btn mt-2">Subscribe</a>
                    <p class="mt-6 text-center text-xs text-gray-500">For large-scale operations and high-volume users</p>
                </div>

            </div>
        </div>
    </section>

    <!-- Interactive Testimonials Section -->
    <section class="py-16 px-4" id="testimonials-section">
        <div class="container mx-auto max-w-7xl">

            <!-- Section Header -->
            <div class="text-center mb-16 animate-on-scroll">
                <p class="text-lg font-semibold mb-4" style="color: #3674B5;">TRUSTED BY THOUSANDS</p>
                <h2 class="section-title text-4xl md:text-5xl text-gray-900 mb-6">
                    Real Results from Real Businesses
                </h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                    Join over 5,000+ small businesses who've transformed their e-commerce operations with Sentio
                </p>
            </div>

            <!-- Testimonials Grid -->
            <div class="lg:grid lg:grid-cols-12 lg:gap-16 lg:items-center lg:justify-between">
                <div class="lg:col-span-5 lg:col-start-1">
                    <!-- Title -->
                    <div class="mb-8 animate-slide-left delay-100">
                        <h3 class="mb-2 text-3xl text-gray-800 font-bold lg:text-4xl">
                            Growth that speaks for itself
                        </h3>
                        <p class="text-gray-600">
                            From small startups to established businesses, Sentio delivers measurable results that drive real growth and success.
                        </p>
                    </div>
                    <!-- End Title -->

                    <!-- Blockquote -->
                    <blockquote class="relative testimonial-card p-6 animate-slide-left delay-200">
                        <svg class="quote-icon absolute top-0 start-0 transform -translate-x-6 -translate-y-8 size-16 text-gray-200" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                            <path d="M7.39762 10.3C7.39762 11.0733 7.14888 11.7 6.6514 12.18C6.15392 12.6333 5.52552 12.86 4.76621 12.86C3.84979 12.86 3.09047 12.5533 2.48825 11.94C1.91222 11.3266 1.62421 10.4467 1.62421 9.29999C1.62421 8.07332 1.96459 6.87332 2.64535 5.69999C3.35231 4.49999 4.33418 3.55332 5.59098 2.85999L6.4943 4.25999C5.81354 4.73999 5.26369 5.27332 4.84476 5.85999C4.45201 6.44666 4.19017 7.12666 4.05926 7.89999C4.29491 7.79332 4.56983 7.73999 4.88403 7.73999C5.61716 7.73999 6.21938 7.97999 6.69067 8.45999C7.16197 8.93999 7.39762 9.55333 7.39762 10.3ZM14.6242 10.3C14.6242 11.0733 14.3755 11.7 13.878 12.18C13.3805 12.6333 12.7521 12.86 11.9928 12.86C11.0764 12.86 10.3171 12.5533 9.71484 11.94C9.13881 11.3266 8.85079 10.4467 8.85079 9.29999C8.85079 8.07332 9.19117 6.87332 9.87194 5.69999C10.5789 4.49999 11.5608 3.55332 12.8176 2.85999L13.7209 4.25999C13.0401 4.73999 12.4903 5.27332 12.0713 5.85999C11.6786 6.44666 11.4168 7.12666 11.2858 7.89999C11.5215 7.79332 11.7964 7.73999 12.1106 7.73999C12.8437 7.73999 13.446 7.97999 13.9173 8.45999C14.3886 8.93999 14.6242 9.55333 14.6242 10.3Z" fill="currentColor"/>
                        </svg>

                        <div class="relative z-10">
                            <p class="text-xl italic text-gray-800">
                                "Sentio transformed our online store completely. We went from 50 orders per day to over 200 in just 3 months. The AI-powered insights helped us optimize everything."
                            </p>
                        </div>

                        <footer class="mt-6">
                            <div class="flex items-center gap-x-4">
                                <div class="shrink-0">
                                    <img class="avatar size-12 rounded-full object-cover" src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=facearea&facepad=2&w=320&h=320&q=80" alt="Sarah Chen">
                                </div>
                                <div class="grow">
                                    <div class="font-semibold text-gray-800">Sarah Chen</div>
                                    <div class="text-sm text-gray-500">Founder | Urban Threads Boutique</div>
                                </div>
                            </div>
                        </footer>
                    </blockquote>
                    <!-- End Blockquote -->
                </div>
                <!-- End Col -->

                <div class="mt-10 lg:mt-0 lg:col-span-6 lg:col-end-13">
                    <div class="space-y-6 sm:space-y-8 animate-slide-right delay-300">
                        <!-- Stats Grid -->
                        <ul class="grid grid-cols-2 divide-y divide-y-2 divide-x divide-x-2 divide-gray-200 overflow-hidden">
                            <li class="stat-card flex flex-col -m-0.5 p-4 sm:p-8 cursor-pointer">
                                <div class="stat-number flex items-end gap-x-2 text-3xl sm:text-5xl font-bold text-gray-800 mb-2">
                                    5,000+
                                </div>
                                <p class="text-sm sm:text-base text-gray-600">
                                    businesses trust Sentio daily
                                </p>
                            </li>

                            <li class="stat-card flex flex-col -m-0.5 p-4 sm:p-8 cursor-pointer">
                                <div class="stat-number flex items-end gap-x-2 text-3xl sm:text-5xl font-bold text-gray-800 mb-2">
                                    <svg class="shrink-0 size-5" style="color: #3674B5;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m5 12 7-7 7 7"/><path d="M12 19V5"/></svg>
                                    40%
                                </div>
                                <p class="text-sm sm:text-base text-gray-600">
                                    average sales increase within 6 months
                                </p>
                            </li>

                            <li class="stat-card flex flex-col -m-0.5 p-4 sm:p-8 cursor-pointer">
                                <div class="stat-number flex items-end gap-x-2 text-3xl sm:text-5xl font-bold text-gray-800 mb-2">
                                    90%
                                </div>
                                <p class="text-sm sm:text-base text-gray-600">
                                    time savings on inventory management
                                </p>
                            </li>

                            <li class="stat-card flex flex-col -m-0.5 p-4 sm:p-8 cursor-pointer">
                                <div class="stat-number flex items-end gap-x-2 text-3xl sm:text-5xl font-bold text-gray-800 mb-2">
                                    99.9%
                                </div>
                                <p class="text-sm sm:text-base text-gray-600">
                                    uptime with guaranteed SLA
                                </p>
                            </li>
                        </ul>
                        <!-- End Stats Grid -->
                    </div>
                </div>
                <!-- End Col -->
            </div>
            <!-- End Testimonials Grid -->
        </div>
    </section>

    <!-- Trusted By Section -->
    <div id="trusted" class="pt-4 pb-16 -mt-8">
        <div class="container mx-auto px-4 md:px-8">
            <h3 class="text-center text-xs font-semibold font-inter animate-on-scroll mb-6" style="color: #3674B5;">
                TRUSTED BY LEADING TEAMS
            </h3>
            <div class="w-full animate-on-scroll delay-200">
                <div class="logo-container">
                    <div class="flex animate-scrolling-logos">
                        <!-- First set of logos -->
                        <div class="flex w-max items-center space-x-20 px-10">
                            <!-- Google -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Google.svg"
                                     alt="Google"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- Microsoft -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Microsoft.svg"
                                     alt="Microsoft"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- Amazon -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Amazon.svg"
                                     alt="Amazon"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- Netflix -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Netflix.svg"
                                     alt="Netflix"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- YouTube -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/YouTube.svg"
                                     alt="YouTube"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- Instagram -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Instagram.svg"
                                     alt="Instagram"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- Uber -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Uber.svg"
                                     alt="Uber"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- Spotify -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Spotify.svg"
                                     alt="Spotify"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>

                        <!-- Duplicated set for seamless scroll -->
                        <div class="flex w-max items-center space-x-20 px-10">
                            <!-- Google -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Google.svg"
                                     alt="Google"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- Microsoft -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Microsoft.svg"
                                     alt="Microsoft"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- Amazon -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Amazon.svg"
                                     alt="Amazon"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- Netflix -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Netflix.svg"
                                     alt="Netflix"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- YouTube -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/YouTube.svg"
                                     alt="YouTube"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- Instagram -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Instagram.svg"
                                     alt="Instagram"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- Uber -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Uber.svg"
                                     alt="Uber"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>

                            <!-- Spotify -->
                            <div class="brand-logo flex items-center justify-center h-10 w-40">
                                <img src="https://cdn.magicui.design/companies/Spotify.svg"
                                     alt="Spotify"
                                     class="h-10 w-40 px-2 filter grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <section class="py-16 px-4" id="faq-section">
        <div class="container mx-auto max-w-7xl">
            <!-- Grid -->
            <div class="grid md:grid-cols-5 gap-10">
                <div class="md:col-span-2">
                    <div class="max-w-xs animate-slide-left">
                        <h2 class="section-title text-2xl font-bold md:text-4xl md:leading-tight text-gray-900">Frequently<br>asked questions</h2>
                        <p class="mt-1 hidden md:block text-gray-600">Answers to the most frequently asked questions about Sentio.</p>
                    </div>
                </div>
                <!-- End Col -->

                <div class="md:col-span-3">
                    <!-- Accordion -->
                    <div class="hs-accordion-group divide-y divide-gray-200 animate-slide-right delay-200">
                        <div class="hs-accordion pb-3 active" id="hs-basic-with-title-and-arrow-stretched-heading-one">
                            <button class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-gray-800 rounded-lg transition hover:text-gray-500 focus:outline-hidden focus:text-gray-500" aria-expanded="true" aria-controls="hs-basic-with-title-and-arrow-stretched-collapse-one">
                                Can I cancel at anytime?
                                <svg class="hs-accordion-active:hidden block shrink-0 size-5 text-gray-600 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                                <svg class="hs-accordion-active:block hidden shrink-0 size-5 text-gray-600 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 15-6-6-6 6"/></svg>
                            </button>
                            <div id="hs-basic-with-title-and-arrow-stretched-collapse-one" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300" role="region" aria-labelledby="hs-basic-with-title-and-arrow-stretched-heading-one">
                                <p class="text-gray-600">
                                    Yes, you can cancel your Sentio subscription anytime with no questions asked. We'd appreciate feedback to help us improve, but it's completely optional.
                                </p>
                            </div>
                        </div>

                        <div class="hs-accordion pt-6 pb-3" id="hs-basic-with-title-and-arrow-stretched-heading-two">
                            <button class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-gray-800 rounded-lg transition hover:text-gray-500 focus:outline-hidden focus:text-gray-500" aria-expanded="false" aria-controls="hs-basic-with-title-and-arrow-stretched-collapse-two">
                                How quickly can I get my store online?
                                <svg class="hs-accordion-active:hidden block shrink-0 size-5 text-gray-600 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                                <svg class="hs-accordion-active:block hidden shrink-0 size-5 text-gray-600 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 15-6-6-6 6"/></svg>
                            </button>
                            <div id="hs-basic-with-title-and-arrow-stretched-collapse-two" class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300" role="region" aria-labelledby="hs-basic-with-title-and-arrow-stretched-heading-two">
                                <p class="text-gray-600">
                                    Most businesses can launch their Sentio-powered store within 24 hours. Our AI-driven setup process handles the technical details while you focus on your products and branding.
                                </p>
                            </div>
                        </div>

                        <div class="hs-accordion pt-6 pb-3" id="hs-basic-with-title-and-arrow-stretched-heading-three">
                            <button class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-gray-800 rounded-lg transition hover:text-gray-500 focus:outline-hidden focus:text-gray-500" aria-expanded="false" aria-controls="hs-basic-with-title-and-arrow-stretched-collapse-three">
                                How does Sentio's pricing work?
                                <svg class="hs-accordion-active:hidden block shrink-0 size-5 text-gray-600 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                                <svg class="hs-accordion-active:block hidden shrink-0 size-5 text-gray-600 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 15-6-6-6 6"/></svg>
                            </button>
                            <div id="hs-basic-with-title-and-arrow-stretched-collapse-three" class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300" role="region" aria-labelledby="hs-basic-with-title-and-arrow-stretched-heading-three">
                                <p class="text-gray-600">
                                    Our pricing is transparent and scalable. Choose from Basic ($9/month), Pro ($29/month), or Enterprise ($99/month) plans based on your business needs. No hidden fees or transaction costs.
                                </p>
                            </div>
                        </div>

                        <div class="hs-accordion pt-6 pb-3" id="hs-basic-with-title-and-arrow-stretched-heading-four">
                            <button class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-gray-800 rounded-lg transition hover:text-gray-500 focus:outline-hidden focus:text-gray-500" aria-expanded="false" aria-controls="hs-basic-with-title-and-arrow-stretched-collapse-four">
                                How secure is Sentio?
                                <svg class="hs-accordion-active:hidden block shrink-0 size-5 text-gray-600 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                                <svg class="hs-accordion-active:block hidden shrink-0 size-5 text-gray-600 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 15-6-6-6 6"/></svg>
                            </button>
                            <div id="hs-basic-with-title-and-arrow-stretched-collapse-four" class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300" role="region" aria-labelledby="hs-basic-with-title-and-arrow-stretched-heading-four">
                                <p class="text-gray-600">
                                    Security is our top priority. Sentio uses bank-level encryption, PCI DSS compliance, and 99.9% uptime guarantee to protect your business and customer data at all times.
                                </p>
                            </div>
                        </div>

                        <div class="hs-accordion pt-6 pb-3" id="hs-basic-with-title-and-arrow-stretched-heading-five">
                            <button class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-gray-800 rounded-lg transition hover:text-gray-500 focus:outline-hidden focus:text-gray-500" aria-expanded="false" aria-controls="hs-basic-with-title-and-arrow-stretched-collapse-five">
                                What integrations does Sentio support?
                                <svg class="hs-accordion-active:hidden block shrink-0 size-5 text-gray-600 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                                <svg class="hs-accordion-active:block hidden shrink-0 size-5 text-gray-600 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 15-6-6-6 6"/></svg>
                            </button>
                            <div id="hs-basic-with-title-and-arrow-stretched-collapse-five" class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300" role="region" aria-labelledby="hs-basic-with-title-and-arrow-stretched-heading-five">
                                <p class="text-gray-600">
                                    Sentio integrates with 200+ popular tools including Stripe, PayPal, Shopify, WooCommerce, Mailchimp, Google Analytics, and major shipping providers. Our API allows custom integrations too.
                                </p>
                            </div>
                        </div>

                        <div class="hs-accordion pt-6 pb-3" id="hs-basic-with-title-and-arrow-stretched-heading-six">
                            <button class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-gray-800 rounded-lg transition hover:text-gray-500 focus:outline-hidden focus:text-gray-500" aria-expanded="false" aria-controls="hs-basic-with-title-and-arrow-stretched-collapse-six">
                                Do you offer customer support?
                                <svg class="hs-accordion-active:hidden block shrink-0 size-5 text-gray-600 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                                <svg class="hs-accordion-active:block hidden shrink-0 size-5 text-gray-600 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 15-6-6-6 6"/></svg>
                            </button>
                            <div id="hs-basic-with-title-and-arrow-stretched-collapse-six" class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300" role="region" aria-labelledby="hs-basic-with-title-and-arrow-stretched-heading-six">
                                <p class="text-gray-600">
                                    Yes! We provide 24/7 customer support via live chat, email, and phone. Pro and Enterprise plans include priority support with dedicated account managers and faster response times.
                                </p>
                            </div>
                        </div>
                    </div>
                    <!-- End Accordion -->
                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->
        </div>
    </section>

    <!-- CTA Section -->
    <section class="relative overflow-hidden cta-background" id="cta-section">
        <div class="cta-top-fade"></div>
        <div class="max-w-[85rem] mx-auto px-4 sm:px-6 lg:px-8 pt-24 pb-10">
            <!-- Announcement Banner -->
            <div class="flex justify-center animate-on-scroll delay-100">
                <a class="inline-flex items-center gap-x-2 bg-white border border-gray-200 text-xs text-gray-600 p-2 px-3 rounded-full transition hover:border-gray-300 focus:outline-hidden focus:border-gray-300" href="#">
                    Transform Your Business Today
                    <span class="flex items-center gap-x-1">
                        <span class="border-s border-gray-200 text-brand ps-2">Start Now</span>
                        <svg class="shrink-0 size-4 text-brand" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>
                    </span>
                </a>
            </div>
            <!-- End Announcement Banner -->

            <!-- Title -->
            <div class="mt-5 max-w-xl text-center mx-auto animate-on-scroll delay-200">
                <h1 class="block font-bold text-gray-800 text-4xl md:text-5xl lg:text-6xl">
                    Ready to Transform Your Business?
                </h1>
            </div>
            <!-- End Title -->

            <div class="mt-5 max-w-3xl text-center mx-auto animate-on-scroll delay-300">
                <p class="text-lg text-gray-600">Join thousands of successful businesses using Sentio to scale their e-commerce operations. Start your free trial today.</p>
            </div>

            <!-- Buttons -->
            <div class="mt-8 flex justify-center animate-scale-up delay-400">
                <button class="hybrid-btn cta-primary-btn group">
                    Start Free Trial

                    <!-- Bottom accent line -->
                    <span
                        class="absolute bottom-0 left-[15%] w-[70%] h-px opacity-30 group-hover:opacity-100 transition-all duration-1000 ease-out"
                        style="background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0) 100%);"
                    ></span>

                    <!-- Wave animations -->
                    <span
                        class="absolute w-52 h-36 -top-10 -left-2 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform group-hover:duration-500 duration-1000 origin-bottom"
                        style="background-color: #7ba3d1;"
                    ></span>
                    <span
                        class="absolute w-52 h-36 -top-10 -left-2 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform group-hover:duration-700 duration-700 origin-bottom"
                        style="background-color: #5a8bc4;"
                    ></span>
                    <span
                        class="absolute w-52 h-36 -top-10 -left-2 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform group-hover:duration-1000 duration-500 origin-bottom"
                        style="background-color: #3674B5;"
                    ></span>

                    <!-- Hover text that fades in on hover -->
                    <span
                        class="group-hover:opacity-100 group-hover:duration-1000 duration-100 opacity-0 absolute inset-0 z-10 flex items-center justify-center"
                    >
                        Get Started!
                    </span>
                </button>
            </div>
            <!-- End Buttons -->

            <!-- Trust Indicators -->
            <div class="mt-8 flex flex-col sm:flex-row items-center justify-center gap-6 text-gray-500 text-sm animate-on-scroll delay-500 fast">
                <div class="flex items-center gap-2">
                    <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <span>No credit card required</span>
                </div>
                <div class="flex items-center gap-2">
                    <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <span>14-day free trial</span>
                </div>
                <div class="flex items-center gap-2">
                    <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <span>Cancel anytime</span>
                </div>
            </div>
            <!-- End Trust Indicators -->
        </div>
    </section>
    <!-- End CTA Section -->

    <!-- ========== ENHANCED MINIMALISTIC FOOTER ========== -->
    <footer class="bg-white border-t border-gray-100 py-16 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <!-- Main Footer Content -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">

          <!-- Brand Section -->
          <div class="lg:col-span-1">
            <div class="mb-6">
              <a href="#hero" class="text-2xl font-bold text-black font-inter hover:text-brand-blue transition-colors duration-300">
                Sentio
              </a>
            </div>
            <p class="text-gray-600 font-inter text-sm leading-relaxed mb-6 max-w-sm">
              Empowering small businesses with AI-driven e-commerce solutions. Build, grow, and scale your online store with confidence.
            </p>
            <div class="flex items-center space-x-4">
              <a href="#" class="text-gray-400 hover:text-brand-blue transition-colors duration-300" aria-label="Twitter">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                </svg>
              </a>
              <a href="#" class="text-gray-400 hover:text-brand-blue transition-colors duration-300" aria-label="LinkedIn">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
              <a href="#" class="text-gray-400 hover:text-brand-blue transition-colors duration-300" aria-label="GitHub">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
              </a>
            </div>
          </div>

          <!-- Navigation Links -->
          <div>
            <h3 class="text-sm font-semibold text-gray-900 font-inter uppercase tracking-wider mb-4">
              Navigation
            </h3>
            <ul class="space-y-3">
              <li>
                <a href="#hero" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  Home
                </a>
              </li>
              <li>
                <a href="#built-for-success" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  Features
                </a>
              </li>
              <li>
                <a href="#platform-impact" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  Benefits
                </a>
              </li>
              <li>
                <a href="#pricing-section" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  Pricing
                </a>
              </li>
              <li>
                <a href="#testimonials-section" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  Testimonials
                </a>
              </li>
            </ul>
          </div>

          <!-- Resources -->
          <div>
            <h3 class="text-sm font-semibold text-gray-900 font-inter uppercase tracking-wider mb-4">
              Resources
            </h3>
            <ul class="space-y-3">
              <li>
                <a href="#faq-section" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  FAQ
                </a>
              </li>
              <li>
                <a href="#" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  Documentation
                </a>
              </li>
              <li>
                <a href="#" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  API Reference
                </a>
              </li>
              <li>
                <a href="#" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  Help Center
                </a>
              </li>
              <li>
                <a href="#" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  Blog
                </a>
              </li>
            </ul>
          </div>

          <!-- Company -->
          <div>
            <h3 class="text-sm font-semibold text-gray-900 font-inter uppercase tracking-wider mb-4">
              Company
            </h3>
            <ul class="space-y-3">
              <li>
                <a href="#" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  About Us
                </a>
              </li>
              <li>
                <a href="#" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  Careers
                </a>
              </li>
              <li>
                <a href="#" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  Contact
                </a>
              </li>
              <li>
                <a href="#" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  Privacy Policy
                </a>
              </li>
              <li>
                <a href="#" class="text-gray-600 hover:text-brand-blue font-inter text-sm transition-colors duration-300">
                  Terms of Service
                </a>
              </li>
            </ul>
          </div>
        </div>

        <!-- Footer Bottom -->
        <div class="mt-12 pt-8 border-t border-gray-100">
          <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p class="text-gray-500 font-inter text-sm">
              © 2024 Sentio. All rights reserved.
            </p>
            <div class="flex items-center space-x-6">
              <span class="text-gray-400 font-inter text-xs">
                Secure payments powered by SSL encryption
              </span>
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                <span class="text-gray-500 font-inter text-xs">
                  99.9% Uptime
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
    <!-- ========== END ENHANCED FOOTER ========== -->

    <!-- Gemini AI Chat Widget -->
    <div id="geminiChatWidget" class="fixed bottom-6 right-6 z-50">
        <!-- Chat Toggle Button -->
        <button id="chatToggleBtn" class="bg-brand-blue hover:bg-blue-700 text-white rounded-full p-4 shadow-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300">
            <svg id="chatIcon" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            <svg id="closeIcon" class="w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>

        <!-- Chat Window -->
        <div id="chatWindow" class="hidden absolute bottom-16 right-0 w-80 h-96 bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col transform transition-all duration-300 ease-out opacity-0 scale-95 translate-y-4">
            <!-- Chat Header -->
            <div class="bg-brand-blue text-white p-4 rounded-t-lg flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-semibold text-sm">Sentio AI Assistant</h3>
                        <p class="text-xs opacity-90">Powered by Gemini</p>
                    </div>
                </div>
                <div class="flex items-center space-x-1">
                    <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span class="text-xs">Online</span>
                </div>
            </div>

            <!-- Chat Messages -->
            <div id="chatMessages" class="flex-1 overflow-y-auto p-4 space-y-3">
                <!-- Welcome message -->
                <div class="flex items-start space-x-2">
                    <div class="w-6 h-6 bg-brand-blue rounded-full flex items-center justify-center flex-shrink-0">
                        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"/>
                        </svg>
                    </div>
                    <div class="bg-gray-100 rounded-lg p-3 max-w-xs">
                        <p class="text-sm text-gray-800">Hi! I'm your Sentio AI assistant. I can help you with questions about our e-commerce platform, products, and services. How can I assist you today?</p>
                    </div>
                </div>
            </div>

            <!-- Typing Indicator -->
            <div id="typingIndicator" class="hidden px-4 pb-2 transform transition-all duration-200 ease-in-out">
                <div class="flex items-start space-x-2">
                    <div class="w-6 h-6 bg-brand-blue rounded-full flex items-center justify-center flex-shrink-0">
                        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"/>
                        </svg>
                    </div>
                    <div class="bg-gray-100 rounded-lg p-3">
                        <div class="flex items-center space-x-1">
                            <span class="text-xs text-gray-600 mr-2">AI is thinking</span>
                            <div class="w-1.5 h-1.5 bg-brand-blue rounded-full animate-bounce"></div>
                            <div class="w-1.5 h-1.5 bg-brand-blue rounded-full animate-bounce" style="animation-delay: 0.15s"></div>
                            <div class="w-1.5 h-1.5 bg-brand-blue rounded-full animate-bounce" style="animation-delay: 0.3s"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="border-t border-gray-200 p-4">
                <div class="flex space-x-2">
                    <input
                        type="text"
                        id="chatInput"
                        placeholder="Type your message..."
                        class="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-transparent"
                        maxlength="500"
                    >
                    <button
                        id="sendBtn"
                        class="bg-brand-blue hover:bg-blue-700 text-white rounded-lg px-4 py-2 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-brand-blue focus:ring-offset-2"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </button>
                </div>
                <p class="text-xs text-gray-500 mt-2">Powered by Google Gemini AI</p>
            </div>
        </div>
    </div>

    <!-- Gemini AI Chat Functionality -->
    <script>
        // Wait for the page to load and Gemini AI to be available
        document.addEventListener('DOMContentLoaded', function() {
            // Chat widget elements
            const chatToggleBtn = document.getElementById('chatToggleBtn');
            const chatWindow = document.getElementById('chatWindow');
            const chatIcon = document.getElementById('chatIcon');
            const closeIcon = document.getElementById('closeIcon');
            const chatInput = document.getElementById('chatInput');
            const sendBtn = document.getElementById('sendBtn');
            const chatMessages = document.getElementById('chatMessages');
            const typingIndicator = document.getElementById('typingIndicator');

            let isChatOpen = false;

            // Toggle chat window with smooth animations
            chatToggleBtn.addEventListener('click', function() {
                isChatOpen = !isChatOpen;

                if (isChatOpen) {
                    // Opening animation
                    chatWindow.classList.remove('hidden');
                    chatIcon.classList.add('hidden');
                    closeIcon.classList.remove('hidden');

                    // Trigger animation after a brief delay to ensure element is rendered
                    setTimeout(() => {
                        chatWindow.classList.remove('opacity-0', 'scale-95', 'translate-y-4');
                        chatWindow.classList.add('opacity-100', 'scale-100', 'translate-y-0');
                    }, 10);

                    // Focus input after animation
                    setTimeout(() => {
                        chatInput.focus();
                    }, 150);
                } else {
                    // Closing animation
                    chatWindow.classList.remove('opacity-100', 'scale-100', 'translate-y-0');
                    chatWindow.classList.add('opacity-0', 'scale-95', 'translate-y-4');
                    chatIcon.classList.remove('hidden');
                    closeIcon.classList.add('hidden');

                    // Hide element after animation completes
                    setTimeout(() => {
                        chatWindow.classList.add('hidden');
                    }, 300);
                }
            });

            // Send message function
            async function sendMessage() {
                const message = chatInput.value.trim();
                if (!message) return;

                // Add user message to chat
                addMessage(message, 'user');
                chatInput.value = '';

                // Show typing indicator with thinking delay
                showTypingIndicator();

                // Add a realistic thinking delay (1-2 seconds) to make it feel more natural
                const thinkingDelay = Math.random() * 1000 + 1000; // Random delay between 1-2 seconds

                await new Promise(resolve => setTimeout(resolve, thinkingDelay));

                try {
                    // Check if Gemini AI is available
                    if (!window.genAI) {
                        throw new Error('Gemini AI is not initialized');
                    }

                    // Create context for the AI about Sentio
                    const context = `You are a helpful AI assistant for Sentio, an e-commerce platform that helps small businesses launch and grow their online stores.

Key features of Sentio:
- AI-powered inventory management and forecasting
- Smart analytics and sales tracking
- 24/7 customer support
- Secure payment processing
- Quick 2-hour setup process
- Integration with 200+ popular tools
- Predictive analytics and customer insights

Please provide helpful, friendly responses about Sentio's features, pricing, or general e-commerce questions. Keep responses concise and professional.

User question: ${message}`;

                    // Generate response using Gemini
                    const response = await window.genAI.models.generateContent({
                        model: 'gemini-2.0-flash-exp',
                        contents: context
                    });

                    const aiResponse = response.text || 'I apologize, but I encountered an issue generating a response. Please try again.';

                    // Hide typing indicator and add AI response
                    hideTypingIndicator();
                    addMessage(aiResponse, 'ai');

                } catch (error) {
                    console.error('Error generating AI response:', error);
                    hideTypingIndicator();

                    let errorMessage = 'I apologize, but I\'m having trouble connecting right now. ';

                    if (error.message.includes('API key')) {
                        errorMessage += 'There seems to be an issue with the API configuration.';
                    } else if (error.message.includes('quota') || error.message.includes('limit')) {
                        errorMessage += 'The service is currently at capacity. Please try again in a moment.';
                    } else {
                        errorMessage += 'Please try again in a moment or contact our support team for assistance.';
                    }

                    addMessage(errorMessage, 'ai');
                }
            }

            // Add message to chat with smooth animation
            function addMessage(text, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex items-start space-x-2 transform transition-all duration-300 ease-out opacity-0 translate-y-2';

                if (sender === 'user') {
                    messageDiv.innerHTML = `
                        <div class="flex-1"></div>
                        <div class="bg-brand-blue text-white rounded-lg p-3 max-w-xs">
                            <p class="text-sm">${escapeHtml(text)}</p>
                        </div>
                        <div class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="w-6 h-6 bg-brand-blue rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"/>
                            </svg>
                        </div>
                        <div class="bg-gray-100 rounded-lg p-3 max-w-xs">
                            <p class="text-sm text-gray-800">${escapeHtml(text)}</p>
                        </div>
                    `;
                }

                chatMessages.appendChild(messageDiv);

                // Trigger animation after element is added to DOM
                setTimeout(() => {
                    messageDiv.classList.remove('opacity-0', 'translate-y-2');
                    messageDiv.classList.add('opacity-100', 'translate-y-0');
                }, 10);

                // Scroll to bottom after animation
                setTimeout(() => {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }, 150);
            }

            // Show typing indicator
            function showTypingIndicator() {
                typingIndicator.classList.remove('hidden');
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Hide typing indicator
            function hideTypingIndicator() {
                typingIndicator.classList.add('hidden');
            }

            // Escape HTML to prevent XSS
            function escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // Event listeners
            sendBtn.addEventListener('click', sendMessage);

            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Close chat when clicking outside with smooth animation
            document.addEventListener('click', function(e) {
                if (isChatOpen && !chatToggleBtn.contains(e.target) && !chatWindow.contains(e.target)) {
                    isChatOpen = false;

                    // Closing animation
                    chatWindow.classList.remove('opacity-100', 'scale-100', 'translate-y-0');
                    chatWindow.classList.add('opacity-0', 'scale-95', 'translate-y-4');
                    chatIcon.classList.remove('hidden');
                    closeIcon.classList.add('hidden');

                    // Hide element after animation completes
                    setTimeout(() => {
                        chatWindow.classList.add('hidden');
                    }, 300);
                }
            });
        });
    </script>

    <!-- External JavaScript -->
    <script src="script.js"></script>

</body>
</html>
