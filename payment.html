<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Start Free Trial - Sentio | Payment Details</title>
    <meta name="description" content="Start your free trial with Sentio. Enter your payment details to begin your 14-day free trial of our e-commerce platform.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'brand-blue': '#3674B5',
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'dm-sans': ['DM Sans', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&family=DM+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Lordicon -->
    <script src="https://cdn.lordicon.com/lordicon.js"></script>
    
    <!-- External CSS -->
    <link rel="stylesheet" href="styles.css">

    <!-- Custom Animations -->
    <style>
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .animate-on-scroll.delay-100 {
            animation-delay: 0.1s;
        }

        .animate-on-scroll.delay-200 {
            animation-delay: 0.2s;
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .group:hover .group-hover\:scale-105 {
            transform: scale(1.05);
        }
    </style>
</head>

<body class="bg-gray-50 font-inter">


    <!-- Main Content -->
    <main class="min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-blue-50">
        <div class="max-w-6xl mx-auto">
            <!-- Back to Home Link -->
            <div class="text-center mb-8 animate-on-scroll">
                <a href="Testing.html" class="inline-flex items-center text-brand-blue hover:text-blue-700 transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Sentio
                </a>
            </div>

            <!-- Header Section -->
            <div class="text-center mb-12 animate-on-scroll delay-100">
                <div class="mx-auto h-20 w-20 bg-gradient-to-r from-brand-blue to-blue-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg transform transition-transform duration-300 hover:scale-105">
                    <svg class="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                </div>
                <h1 class="text-5xl font-bold text-gray-900 font-dm-sans mb-4">
                    Start Your Free Trial
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Begin your 14-day free trial today. No charges until your trial ends, and you can cancel anytime.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-12 animate-on-scroll delay-200">
                <!-- Payment Form -->
                <div class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                    <h2 class="text-3xl font-bold text-gray-900 font-dm-sans mb-8 text-center">
                        Payment Information
                    </h2>

                    <form id="paymentForm" class="space-y-6">
                        <!-- Account Information -->
                        <div class="space-y-5">
                            <h3 class="text-xl font-bold text-gray-900 flex items-center mb-4">
                                <svg class="w-5 h-5 mr-2 text-brand-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                Account Details
                            </h3>

                            <!-- Email -->
                            <div class="group">
                                <label for="email" class="block text-sm font-semibold text-gray-700 mb-2 transition-colors duration-200 group-focus-within:text-brand-blue">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                    </svg>
                                    Email Address
                                </label>
                                <input
                                    id="email"
                                    name="email"
                                    type="email"
                                    required
                                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-0 focus:border-brand-blue transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white"
                                    placeholder="Enter your email address"
                                >
                            </div>

                            <!-- Business Name -->
                            <div class="group">
                                <label for="businessName" class="block text-sm font-semibold text-gray-700 mb-2 transition-colors duration-200 group-focus-within:text-brand-blue">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    Business Name
                                </label>
                                <input
                                    id="businessName"
                                    name="businessName"
                                    type="text"
                                    required
                                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-0 focus:border-brand-blue transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white"
                                    placeholder="Enter your business name"
                                >
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                                Payment Method
                            </h3>
                            
                            <!-- Card Number -->
                            <div>
                                <label for="cardNumber" class="block text-sm font-medium text-gray-700 mb-1">
                                    Card Number
                                </label>
                                <div class="relative">
                                    <input 
                                        id="cardNumber" 
                                        name="cardNumber" 
                                        type="text" 
                                        required 
                                        maxlength="19"
                                        class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue transition-all duration-200 pr-12"
                                        placeholder="1234 5678 9012 3456"
                                    >
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <!-- Expiry and CVV -->
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="expiry" class="block text-sm font-medium text-gray-700 mb-1">
                                        Expiry Date
                                    </label>
                                    <input 
                                        id="expiry" 
                                        name="expiry" 
                                        type="text" 
                                        required 
                                        maxlength="5"
                                        class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue transition-all duration-200"
                                        placeholder="MM/YY"
                                    >
                                </div>
                                <div>
                                    <label for="cvv" class="block text-sm font-medium text-gray-700 mb-1">
                                        CVV
                                    </label>
                                    <input 
                                        id="cvv" 
                                        name="cvv" 
                                        type="text" 
                                        required 
                                        maxlength="4"
                                        class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue transition-all duration-200"
                                        placeholder="123"
                                    >
                                </div>
                            </div>

                            <!-- Cardholder Name -->
                            <div>
                                <label for="cardholderName" class="block text-sm font-medium text-gray-700 mb-1">
                                    Cardholder Name
                                </label>
                                <input 
                                    id="cardholderName" 
                                    name="cardholderName" 
                                    type="text" 
                                    required 
                                    class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue transition-all duration-200"
                                    placeholder="Name as it appears on card"
                                >
                            </div>
                        </div>

                        <!-- Billing Address -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                                Billing Address
                            </h3>
                            
                            <!-- Address -->
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700 mb-1">
                                    Street Address
                                </label>
                                <input 
                                    id="address" 
                                    name="address" 
                                    type="text" 
                                    required 
                                    class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue transition-all duration-200"
                                    placeholder="123 Main Street"
                                >
                            </div>

                            <!-- City, State, ZIP -->
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="city" class="block text-sm font-medium text-gray-700 mb-1">
                                        City
                                    </label>
                                    <input 
                                        id="city" 
                                        name="city" 
                                        type="text" 
                                        required 
                                        class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue transition-all duration-200"
                                        placeholder="New York"
                                    >
                                </div>
                                <div>
                                    <label for="zipCode" class="block text-sm font-medium text-gray-700 mb-1">
                                        ZIP Code
                                    </label>
                                    <input 
                                        id="zipCode" 
                                        name="zipCode" 
                                        type="text" 
                                        required 
                                        class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue transition-all duration-200"
                                        placeholder="10001"
                                    >
                                </div>
                            </div>

                            <!-- Country -->
                            <div>
                                <label for="country" class="block text-sm font-medium text-gray-700 mb-1">
                                    Country
                                </label>
                                <select 
                                    id="country" 
                                    name="country" 
                                    required 
                                    class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue transition-all duration-200"
                                >
                                    <option value="">Select Country</option>
                                    <option value="US">United States</option>
                                    <option value="CA">Canada</option>
                                    <option value="GB">United Kingdom</option>
                                    <option value="AU">Australia</option>
                                    <option value="DE">Germany</option>
                                    <option value="FR">France</option>
                                    <option value="JP">Japan</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>

                        <!-- Terms -->
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input 
                                    id="terms" 
                                    name="terms" 
                                    type="checkbox" 
                                    required
                                    class="focus:ring-brand-blue h-4 w-4 text-brand-blue border-gray-300 rounded"
                                >
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="terms" class="text-gray-600">
                                    I agree to the 
                                    <a href="#" class="text-brand-blue hover:text-blue-700 font-medium">Terms of Service</a>, 
                                    <a href="#" class="text-brand-blue hover:text-blue-700 font-medium">Privacy Policy</a>, and 
                                    <a href="#" class="text-brand-blue hover:text-blue-700 font-medium">Billing Terms</a>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <button
                            type="submit"
                            class="w-full bg-gradient-to-r from-brand-blue to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-blue shadow-lg hover:shadow-xl"
                        >
                            <span class="flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                Start Free Trial
                            </span>
                        </button>
                    </form>
                </div>

                <!-- Order Summary -->
                <div class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100 animate-on-scroll delay-300">
                    <h2 class="text-3xl font-bold text-gray-900 font-dm-sans mb-8 text-center">
                        Trial Summary
                    </h2>

                    <!-- Plan Details -->
                    <div class="bg-gradient-to-r from-brand-blue to-blue-600 rounded-lg p-6 text-white mb-6">
                        <h3 class="text-xl font-bold mb-2">Sentio Pro Plan</h3>
                        <p class="text-blue-100 mb-4">Everything you need to launch and grow your e-commerce business</p>
                        <div class="text-3xl font-bold">
                            $0 <span class="text-lg font-normal text-blue-100">for 14 days</span>
                        </div>
                        <p class="text-sm text-blue-100 mt-2">Then $49/month, cancel anytime</p>
                    </div>

                    <!-- Features Included -->
                    <div class="space-y-4 mb-6">
                        <h4 class="font-semibold text-gray-900">What's included:</h4>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-gray-700">Unlimited products & orders</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-gray-700">AI-powered analytics</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-gray-700">24/7 customer support</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-gray-700">200+ integrations</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-gray-700">Advanced security features</span>
                            </div>
                        </div>
                    </div>

                    <!-- Security Notice -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                            <div>
                                <h5 class="font-medium text-gray-900 mb-1">Secure Payment</h5>
                                <p class="text-sm text-gray-600">Your payment information is encrypted and secure. We never store your card details.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Payment Form JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const paymentForm = document.getElementById('paymentForm');
            const submitButton = paymentForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;

            // Format card number input
            const cardNumberInput = document.getElementById('cardNumber');
            cardNumberInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
                let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
                if (formattedValue.length > 19) formattedValue = formattedValue.substr(0, 19);
                e.target.value = formattedValue;
            });

            // Format expiry date input
            const expiryInput = document.getElementById('expiry');
            expiryInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length >= 2) {
                    value = value.substring(0, 2) + '/' + value.substring(2, 4);
                }
                e.target.value = value;
            });

            // Format CVV input
            const cvvInput = document.getElementById('cvv');
            cvvInput.addEventListener('input', function(e) {
                e.target.value = e.target.value.replace(/\D/g, '');
            });

            // Form validation
            function validateForm() {
                const email = document.getElementById('email').value.trim();
                const businessName = document.getElementById('businessName').value.trim();
                const cardNumber = document.getElementById('cardNumber').value.replace(/\s/g, '');
                const expiry = document.getElementById('expiry').value;
                const cvv = document.getElementById('cvv').value;
                const cardholderName = document.getElementById('cardholderName').value.trim();
                const address = document.getElementById('address').value.trim();
                const city = document.getElementById('city').value.trim();
                const zipCode = document.getElementById('zipCode').value.trim();
                const country = document.getElementById('country').value;
                const terms = document.getElementById('terms').checked;

                // Reset previous error states
                clearErrors();

                let isValid = true;
                const errors = [];

                // Validate email
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    showFieldError('email', 'Please enter a valid email address');
                    errors.push('Valid email is required');
                    isValid = false;
                }

                // Validate business name
                if (businessName.length < 2) {
                    showFieldError('businessName', 'Please enter your business name');
                    errors.push('Business name is required');
                    isValid = false;
                }

                // Validate card number (basic Luhn algorithm check)
                if (cardNumber.length < 13 || cardNumber.length > 19 || !isValidCardNumber(cardNumber)) {
                    showFieldError('cardNumber', 'Please enter a valid card number');
                    errors.push('Valid card number is required');
                    isValid = false;
                }

                // Validate expiry date
                if (!isValidExpiryDate(expiry)) {
                    showFieldError('expiry', 'Please enter a valid expiry date (MM/YY)');
                    errors.push('Valid expiry date is required');
                    isValid = false;
                }

                // Validate CVV
                if (cvv.length < 3 || cvv.length > 4) {
                    showFieldError('cvv', 'Please enter a valid CVV');
                    errors.push('Valid CVV is required');
                    isValid = false;
                }

                // Validate cardholder name
                if (cardholderName.length < 2) {
                    showFieldError('cardholderName', 'Please enter the cardholder name');
                    errors.push('Cardholder name is required');
                    isValid = false;
                }

                // Validate address
                if (address.length < 5) {
                    showFieldError('address', 'Please enter a valid address');
                    errors.push('Valid address is required');
                    isValid = false;
                }

                // Validate city
                if (city.length < 2) {
                    showFieldError('city', 'Please enter a valid city');
                    errors.push('Valid city is required');
                    isValid = false;
                }

                // Validate ZIP code
                if (zipCode.length < 3) {
                    showFieldError('zipCode', 'Please enter a valid ZIP code');
                    errors.push('Valid ZIP code is required');
                    isValid = false;
                }

                // Validate country
                if (!country) {
                    showFieldError('country', 'Please select your country');
                    errors.push('Country selection is required');
                    isValid = false;
                }

                // Validate terms acceptance
                if (!terms) {
                    showFieldError('terms', 'You must agree to the terms and conditions');
                    errors.push('Terms acceptance is required');
                    isValid = false;
                }

                return isValid;
            }

            // Basic Luhn algorithm for card validation
            function isValidCardNumber(cardNumber) {
                let sum = 0;
                let isEven = false;

                for (let i = cardNumber.length - 1; i >= 0; i--) {
                    let digit = parseInt(cardNumber.charAt(i));

                    if (isEven) {
                        digit *= 2;
                        if (digit > 9) {
                            digit -= 9;
                        }
                    }

                    sum += digit;
                    isEven = !isEven;
                }

                return sum % 10 === 0;
            }

            // Validate expiry date
            function isValidExpiryDate(expiry) {
                if (!/^\d{2}\/\d{2}$/.test(expiry)) return false;

                const [month, year] = expiry.split('/').map(num => parseInt(num));
                const currentDate = new Date();
                const currentYear = currentDate.getFullYear() % 100;
                const currentMonth = currentDate.getMonth() + 1;

                if (month < 1 || month > 12) return false;
                if (year < currentYear || (year === currentYear && month < currentMonth)) return false;

                return true;
            }

            // Show field error
            function showFieldError(fieldId, message) {
                const field = document.getElementById(fieldId);
                field.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
                field.classList.remove('border-gray-300', 'focus:ring-brand-blue', 'focus:border-brand-blue');

                // Add error message if it doesn't exist
                let errorMsg = field.parentNode.querySelector('.error-message');
                if (!errorMsg) {
                    errorMsg = document.createElement('p');
                    errorMsg.className = 'error-message text-red-500 text-xs mt-1';
                    field.parentNode.appendChild(errorMsg);
                }
                errorMsg.textContent = message;
            }

            // Clear all errors
            function clearErrors() {
                const fields = ['email', 'businessName', 'cardNumber', 'expiry', 'cvv', 'cardholderName', 'address', 'city', 'zipCode', 'country'];
                fields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
                    field.classList.add('border-gray-300', 'focus:ring-brand-blue', 'focus:border-brand-blue');

                    const errorMsg = field.parentNode.querySelector('.error-message');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                });

                // Clear terms error
                const termsError = document.getElementById('terms').parentNode.parentNode.querySelector('.error-message');
                if (termsError) {
                    termsError.remove();
                }
            }

            // Handle form submission
            paymentForm.addEventListener('submit', function(e) {
                e.preventDefault();

                if (!validateForm()) {
                    return;
                }

                // Redirect to dashboard or success page
                // You can change this URL to wherever you want users to go after payment
                window.location.href = 'Testing.html';
            });

            // Real-time validation feedback
            const fields = ['email', 'businessName', 'cardNumber', 'expiry', 'cvv', 'cardholderName', 'address', 'city', 'zipCode'];
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                field.addEventListener('blur', function() {
                    // Clear previous error for this field
                    field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
                    field.classList.add('border-gray-300', 'focus:ring-brand-blue', 'focus:border-brand-blue');

                    const errorMsg = field.parentNode.querySelector('.error-message');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                });
            });
        });
    </script>

    <!-- External JavaScript -->
    <script src="script.js"></script>
</body>
</html>
