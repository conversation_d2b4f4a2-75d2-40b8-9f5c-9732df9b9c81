<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Sentio | Launch Your E-commerce Store</title>
    <meta name="description" content="Join <PERSON> today and launch your e-commerce store in just 2 hours. Get started with our AI-powered platform for small businesses.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'brand-blue': '#3674B5',
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'dm-sans': ['DM Sans', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&family=DM+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Lordicon -->
    <script src="https://cdn.lordicon.com/lordicon.js"></script>
    
    <!-- External CSS -->
    <link rel="stylesheet" href="styles.css">

    <!-- Custom Animations -->
    <style>
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .animate-on-scroll.delay-100 {
            animation-delay: 0.1s;
        }

        .animate-on-scroll.delay-200 {
            animation-delay: 0.2s;
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .group:hover .group-hover\:scale-105 {
            transform: scale(1.05);
        }
    </style>
</head>

<body class="bg-gray-50 font-inter">


    <!-- Main Content -->
    <main class="min-h-screen flex items-center justify-center py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-blue-50">
        <div class="max-w-2xl w-full space-y-12">
            <!-- Back to Home Link -->
            <div class="text-center animate-on-scroll">
                <a href="Testing.html" class="inline-flex items-center text-brand-blue hover:text-blue-700 transition-colors duration-200 mb-8">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Sentio
                </a>
            </div>

            <!-- Header Section -->
            <div class="text-center animate-on-scroll delay-100">
                <div class="mx-auto h-16 w-16 bg-gradient-to-r from-brand-blue to-blue-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg transform transition-transform duration-300 hover:scale-105">
                    <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h2 class="text-4xl font-bold text-gray-900 font-dm-sans mb-3">
                    Launch Your Store
                </h2>
                <p class="text-lg text-gray-600 max-w-sm mx-auto">
                    Join thousands of entrepreneurs building their e-commerce success with Sentio
                </p>
            </div>

            <!-- Sign Up Form -->
            <div class="bg-white rounded-2xl shadow-xl px-12 py-10 animate-on-scroll delay-200 border border-gray-100">
                <form class="space-y-8" id="signupForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Full Name -->
                        <div class="group">
                            <label for="fullName" class="block text-sm font-semibold text-gray-700 mb-2 transition-colors duration-200 group-focus-within:text-brand-blue">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                Full Name
                            </label>
                            <input
                                id="fullName"
                                name="fullName"
                                type="text"
                                required
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-0 focus:border-brand-blue transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white"
                                placeholder="Enter your full name"
                            >
                        </div>

                        <!-- Email -->
                        <div class="group">
                            <label for="email" class="block text-sm font-semibold text-gray-700 mb-2 transition-colors duration-200 group-focus-within:text-brand-blue">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                </svg>
                                Email Address
                            </label>
                            <input
                                id="email"
                                name="email"
                                type="email"
                                required
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-0 focus:border-brand-blue transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white"
                                placeholder="Enter your email address"
                            >
                        </div>

                        <!-- Business Name -->
                        <div class="group">
                            <label for="businessName" class="block text-sm font-semibold text-gray-700 mb-2 transition-colors duration-200 group-focus-within:text-brand-blue">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                Business Name
                            </label>
                            <input
                                id="businessName"
                                name="businessName"
                                type="text"
                                required
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-0 focus:border-brand-blue transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white"
                                placeholder="Enter your business name"
                            >
                        </div>

                        <!-- Password -->
                        <div class="group">
                            <label for="password" class="block text-sm font-semibold text-gray-700 mb-2 transition-colors duration-200 group-focus-within:text-brand-blue">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                Password
                            </label>
                            <input
                                id="password"
                                name="password"
                                type="password"
                                required
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-0 focus:border-brand-blue transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white"
                                placeholder="Create a secure password"
                            >
                        </div>

                        <!-- Confirm Password -->
                        <div class="group">
                            <label for="confirmPassword" class="block text-sm font-semibold text-gray-700 mb-2 transition-colors duration-200 group-focus-within:text-brand-blue">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Confirm Password
                            </label>
                            <input
                                id="confirmPassword"
                                name="confirmPassword"
                                type="password"
                                required
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-0 focus:border-brand-blue transition-all duration-300 hover:border-gray-300 bg-gray-50 focus:bg-white"
                                placeholder="Confirm your password"
                            >
                        </div>
                    </div>

                    <!-- Terms and Privacy -->
                    <div class="flex items-start justify-center p-6 bg-gray-50 rounded-xl">
                        <div class="flex items-center h-5">
                            <input
                                id="terms"
                                name="terms"
                                type="checkbox"
                                required
                                class="focus:ring-brand-blue h-4 w-4 text-brand-blue border-gray-300 rounded transition-all duration-200"
                            >
                        </div>
                        <div class="ml-3 text-base">
                            <label for="terms" class="text-gray-600">
                                I agree to the
                                <a href="#" class="text-brand-blue hover:text-blue-700 font-semibold transition-colors duration-200">Terms of Service</a>
                                and
                                <a href="#" class="text-brand-blue hover:text-blue-700 font-semibold transition-colors duration-200">Privacy Policy</a>
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="pt-6">
                        <button
                            type="submit"
                            class="group relative w-full flex justify-center items-center py-5 px-8 border border-transparent text-lg font-bold rounded-xl text-white bg-gradient-to-r from-brand-blue to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-blue transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                        >
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            Create Your Store
                        </button>
                    </div>

                    <!-- Login Link -->
                    <div class="text-center pt-8">
                        <p class="text-base text-gray-600">
                            Already have an account?
                            <a href="#" class="font-semibold text-brand-blue hover:text-blue-700 transition-colors duration-200">
                                Sign in here
                            </a>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </main>



    <!-- Signup Form JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const signupForm = document.getElementById('signupForm');
            const submitButton = signupForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;

            // Form validation
            function validateForm() {
                const fullName = document.getElementById('fullName').value.trim();
                const email = document.getElementById('email').value.trim();
                const businessName = document.getElementById('businessName').value.trim();
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                const terms = document.getElementById('terms').checked;

                // Reset previous error states
                clearErrors();

                let isValid = true;
                const errors = [];

                // Validate full name
                if (fullName.length < 2) {
                    showFieldError('fullName', 'Please enter your full name');
                    errors.push('Full name is required');
                    isValid = false;
                }

                // Validate email
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    showFieldError('email', 'Please enter a valid email address');
                    errors.push('Valid email is required');
                    isValid = false;
                }

                // Validate business name
                if (businessName.length < 2) {
                    showFieldError('businessName', 'Please enter your business name');
                    errors.push('Business name is required');
                    isValid = false;
                }

                // Validate password
                if (password.length < 8) {
                    showFieldError('password', 'Password must be at least 8 characters long');
                    errors.push('Password must be at least 8 characters');
                    isValid = false;
                }

                // Validate password confirmation
                if (password !== confirmPassword) {
                    showFieldError('confirmPassword', 'Passwords do not match');
                    errors.push('Passwords must match');
                    isValid = false;
                }

                // Validate terms acceptance
                if (!terms) {
                    showFieldError('terms', 'You must agree to the Terms of Service and Privacy Policy');
                    errors.push('Terms acceptance is required');
                    isValid = false;
                }

                return isValid;
            }

            // Show field error
            function showFieldError(fieldId, message) {
                const field = document.getElementById(fieldId);
                field.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
                field.classList.remove('border-gray-300', 'focus:ring-brand-blue', 'focus:border-brand-blue');

                // Add error message if it doesn't exist
                let errorMsg = field.parentNode.querySelector('.error-message');
                if (!errorMsg) {
                    errorMsg = document.createElement('p');
                    errorMsg.className = 'error-message text-red-500 text-xs mt-1';
                    field.parentNode.appendChild(errorMsg);
                }
                errorMsg.textContent = message;
            }

            // Clear all errors
            function clearErrors() {
                const fields = ['fullName', 'email', 'businessName', 'password', 'confirmPassword'];
                fields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
                    field.classList.add('border-gray-300', 'focus:ring-brand-blue', 'focus:border-brand-blue');

                    const errorMsg = field.parentNode.querySelector('.error-message');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                });

                // Clear terms error
                const termsError = document.getElementById('terms').parentNode.parentNode.querySelector('.error-message');
                if (termsError) {
                    termsError.remove();
                }
            }

            // Handle form submission
            signupForm.addEventListener('submit', function(e) {
                e.preventDefault();

                if (!validateForm()) {
                    return;
                }

                // Show loading state
                submitButton.disabled = true;
                submitButton.innerHTML = `
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </span>
                    Creating Your Account...
                `;

                // Simulate API call
                setTimeout(() => {
                    // Success state
                    submitButton.innerHTML = `
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </span>
                        Account Created Successfully!
                    `;
                    submitButton.classList.remove('bg-brand-blue', 'hover:bg-blue-700');
                    submitButton.classList.add('bg-green-600');

                    // Show success message
                    setTimeout(() => {
                        alert('Welcome to Sentio! Your account has been created successfully. You will be redirected to your dashboard shortly.');
                        // In a real application, you would redirect to the dashboard
                        // window.location.href = 'dashboard.html';
                    }, 1000);

                }, 2000);
            });

            // Real-time validation feedback
            const fields = ['fullName', 'email', 'businessName', 'password', 'confirmPassword'];
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                field.addEventListener('blur', function() {
                    // Clear previous error for this field
                    field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
                    field.classList.add('border-gray-300', 'focus:ring-brand-blue', 'focus:border-brand-blue');

                    const errorMsg = field.parentNode.querySelector('.error-message');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                });
            });
        });
    </script>

    <!-- External JavaScript -->
    <script src="script.js"></script>
</body>
</html>
