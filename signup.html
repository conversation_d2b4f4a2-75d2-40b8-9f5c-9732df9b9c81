<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Sentio | Launch Your E-commerce Store</title>
    <meta name="description" content="Join <PERSON> today and launch your e-commerce store in just 2 hours. Get started with our AI-powered platform for small businesses.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'brand-blue': '#3674B5',
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'dm-sans': ['DM Sans', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&family=DM+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Lordicon -->
    <script src="https://cdn.lordicon.com/lordicon.js"></script>
    
    <!-- External CSS -->
    <link rel="stylesheet" href="styles.css">

    <!-- Custom Animations -->
    <style>
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .animate-on-scroll.delay-100 {
            animation-delay: 0.1s;
        }

        .animate-on-scroll.delay-200 {
            animation-delay: 0.2s;
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .group:hover .group-hover\:scale-105 {
            transform: scale(1.05);
        }
    </style>
</head>

<body class="bg-gray-50 font-inter">


    <!-- Main Content -->
    <main class="min-h-screen flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-400 to-blue-600">
        <div class="max-w-md w-full">
            <!-- Logo/Brand -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-white font-dm-sans tracking-wide">
                    sentio<span class="font-light">experts</span>
                </h1>
            </div>

            <!-- Multi-Step Signup Form -->
            <div class="bg-white rounded-3xl shadow-2xl p-8 relative">
                <!-- Already have account link -->
                <div class="text-center mb-6">
                    <p class="text-sm text-gray-500">
                        Already have an account?
                        <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">Sign in</a>
                    </p>
                </div>

                <!-- Step 1: Email -->
                <div id="step1" class="step-content">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Create an account</h2>

                    <div class="mb-6">
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email address
                        </label>
                        <input
                            id="email"
                            name="email"
                            type="email"
                            required
                            class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg focus:outline-none focus:border-blue-500 transition-colors duration-200"
                            placeholder=""
                        >
                    </div>

                    <div class="mb-6">
                        <label class="flex items-start">
                            <input type="checkbox" id="terms" class="mt-1 mr-3 h-4 w-4 text-blue-500 border-gray-300 rounded">
                            <span class="text-sm text-gray-600">
                                You accept the <a href="#" class="text-blue-500 underline">privacy policy</a> and <a href="#" class="text-blue-500 underline">terms of use</a>
                            </span>
                        </label>
                    </div>

                    <button
                        type="button"
                        id="nextStep1"
                        class="w-full bg-blue-400 hover:bg-blue-500 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                    >
                        Next
                    </button>

                    <div class="mt-8">
                        <p class="text-center text-sm text-gray-500 mb-4">Or log in using your account:</p>
                        <div class="grid grid-cols-4 gap-3">
                            <button class="flex items-center justify-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-5 h-5" viewBox="0 0 24 24">
                                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                </svg>
                            </button>
                            <button class="flex items-center justify-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </button>
                            <button class="flex items-center justify-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                                </svg>
                            </button>
                            <button class="flex items-center justify-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-5 h-5 text-blue-700" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </button>
                        </div>
                        <div class="grid grid-cols-4 gap-3 mt-2 text-xs text-gray-500 text-center">
                            <span>Google</span>
                            <span>Facebook</span>
                            <span>Apple</span>
                            <span>LinkedIn</span>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Personal Info -->
                <div id="step2" class="step-content hidden">
                    <button type="button" id="backStep2" class="mb-4 text-gray-500 hover:text-gray-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </button>

                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Personal Information</h2>

                    <div class="space-y-4 mb-6">
                        <div>
                            <label for="fullName" class="block text-sm font-medium text-gray-700 mb-2">
                                Full Name
                            </label>
                            <input
                                id="fullName"
                                name="fullName"
                                type="text"
                                required
                                class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg focus:outline-none focus:border-blue-500 transition-colors duration-200"
                                placeholder="Enter your full name"
                            >
                        </div>

                        <div>
                            <label for="businessName" class="block text-sm font-medium text-gray-700 mb-2">
                                Business Name
                            </label>
                            <input
                                id="businessName"
                                name="businessName"
                                type="text"
                                required
                                class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg focus:outline-none focus:border-blue-500 transition-colors duration-200"
                                placeholder="Enter your business name"
                            >
                        </div>
                    </div>

                    <button
                        type="button"
                        id="nextStep2"
                        class="w-full bg-blue-400 hover:bg-blue-500 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                    >
                        Next
                    </button>
                </div>

                <!-- Step 3: Password -->
                <div id="step3" class="step-content hidden">
                    <button type="button" id="backStep3" class="mb-4 text-gray-500 hover:text-gray-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </button>

                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Create Password</h2>

                    <div class="space-y-4 mb-6">
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                Create password
                            </label>
                            <input
                                id="password"
                                name="password"
                                type="password"
                                required
                                class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg focus:outline-none focus:border-blue-500 transition-colors duration-200"
                                placeholder=""
                            >

                            <!-- Password Requirements -->
                            <div class="mt-3 space-y-2">
                                <div class="flex items-center text-sm">
                                    <div id="req-length" class="w-4 h-4 rounded-full border-2 border-gray-300 mr-2 flex items-center justify-center">
                                        <svg class="w-2 h-2 text-green-500 hidden" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-gray-600">At least 8 characters</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <div id="req-uppercase" class="w-4 h-4 rounded-full border-2 border-gray-300 mr-2 flex items-center justify-center">
                                        <svg class="w-2 h-2 text-green-500 hidden" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-gray-600">One uppercase letter</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <div id="req-lowercase" class="w-4 h-4 rounded-full border-2 border-gray-300 mr-2 flex items-center justify-center">
                                        <svg class="w-2 h-2 text-green-500 hidden" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-gray-600">One lowercase letter</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <div id="req-number" class="w-4 h-4 rounded-full border-2 border-gray-300 mr-2 flex items-center justify-center">
                                        <svg class="w-2 h-2 text-green-500 hidden" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-gray-600">One number</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <div id="req-special" class="w-4 h-4 rounded-full border-2 border-gray-300 mr-2 flex items-center justify-center">
                                        <svg class="w-2 h-2 text-green-500 hidden" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-gray-600">One special character (!@#$%^&*)</span>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                Re-enter password
                            </label>
                            <input
                                id="confirmPassword"
                                name="confirmPassword"
                                type="password"
                                required
                                class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg focus:outline-none focus:border-blue-500 transition-colors duration-200"
                                placeholder=""
                            >
                        </div>
                    </div>

                    <button
                        type="button"
                        id="nextStep3"
                        class="w-full bg-blue-400 hover:bg-blue-500 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 disabled:bg-gray-300 disabled:cursor-not-allowed"
                        disabled
                    >
                        Next
                    </button>
                </div>

                <!-- Step 4: Email Verification -->
                <div id="step4" class="step-content hidden">
                    <button type="button" id="backStep4" class="mb-4 text-gray-500 hover:text-gray-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </button>

                    <div class="text-center">
                        <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                            <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>

                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Check your email</h2>
                        <p class="text-gray-600 mb-6">
                            We've sent a verification code to<br>
                            <span id="emailDisplay" class="font-medium text-gray-900"></span>
                        </p>

                        <div class="mb-6">
                            <label for="otpCode" class="block text-sm font-medium text-gray-700 mb-2">
                                Verification Code
                            </label>
                            <input
                                id="otpCode"
                                name="otpCode"
                                type="text"
                                maxlength="6"
                                required
                                class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg focus:outline-none focus:border-blue-500 transition-colors duration-200 text-center text-lg tracking-widest"
                                placeholder="000000"
                            >
                        </div>

                        <button
                            type="button"
                            id="verifyOTP"
                            class="w-full bg-blue-400 hover:bg-blue-500 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 mb-4"
                        >
                            Verify & Create Account
                        </button>

                        <p class="text-sm text-gray-500">
                            Didn't receive the code?
                            <button type="button" id="resendOTP" class="text-blue-500 hover:text-blue-600 font-medium">
                                Resend
                            </button>
                        </p>
                    </div>
                </div>

                <!-- Step 5: Success (Optional) -->
                <div id="step5" class="step-content hidden">
                    <div class="text-center">
                        <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                            <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>

                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Check your email</h2>
                        <p class="text-gray-600 mb-6">
                            We've sent a verification code to<br>
                            <span id="emailDisplay" class="font-medium text-gray-900"></span>
                        </p>

                        <div class="mb-6">
                            <label for="otpCode" class="block text-sm font-medium text-gray-700 mb-2">
                                Verification Code
                            </label>
                            <input
                                id="otpCode"
                                name="otpCode"
                                type="text"
                                maxlength="6"
                                required
                                class="w-full px-4 py-3 border-2 border-blue-200 rounded-lg focus:outline-none focus:border-blue-500 transition-colors duration-200 text-center text-lg tracking-widest"
                                placeholder="000000"
                            >
                        </div>

                        <button
                            type="button"
                            id="verifyOTP"
                            class="w-full bg-blue-400 hover:bg-blue-500 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 mb-4"
                        >
                            Verify & Create Account
                        </button>

                        <p class="text-sm text-gray-500">
                            Didn't receive the code?
                            <button type="button" id="resendOTP" class="text-blue-500 hover:text-blue-600 font-medium">
                                Resend
                            </button>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </main>



    <!-- Multi-Step Signup JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let currentStep = 1;
            const totalSteps = 5;
            let formData = {};

            // Step navigation functions
            function showStep(stepNumber) {
                // Hide all steps
                for (let i = 1; i <= totalSteps; i++) {
                    const step = document.getElementById(`step${i}`);
                    if (step) {
                        step.classList.add('hidden');
                    }
                }

                // Show current step
                const currentStepElement = document.getElementById(`step${stepNumber}`);
                if (currentStepElement) {
                    currentStepElement.classList.remove('hidden');
                }

                currentStep = stepNumber;
            }

            function validateEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            function validatePassword(password) {
                const requirements = {
                    length: password.length >= 8,
                    uppercase: /[A-Z]/.test(password),
                    lowercase: /[a-z]/.test(password),
                    number: /\d/.test(password),
                    special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
                };

                return Object.values(requirements).every(req => req);
            }

            function updatePasswordRequirements(password) {
                const requirements = {
                    length: password.length >= 8,
                    uppercase: /[A-Z]/.test(password),
                    lowercase: /[a-z]/.test(password),
                    number: /\d/.test(password),
                    special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
                };

                // Update visual indicators
                Object.keys(requirements).forEach(req => {
                    const indicator = document.getElementById(`req-${req}`);
                    const checkmark = indicator.querySelector('svg');

                    if (requirements[req]) {
                        indicator.classList.remove('border-gray-300');
                        indicator.classList.add('border-green-500', 'bg-green-500');
                        checkmark.classList.remove('hidden');
                    } else {
                        indicator.classList.remove('border-green-500', 'bg-green-500');
                        indicator.classList.add('border-gray-300');
                        checkmark.classList.add('hidden');
                    }
                });

                // Enable/disable next button
                const nextButton = document.getElementById('nextStep3');
                const confirmPassword = document.getElementById('confirmPassword').value;
                const allRequirementsMet = Object.values(requirements).every(req => req);
                const passwordsMatch = password === confirmPassword && confirmPassword !== '';

                if (allRequirementsMet && passwordsMatch) {
                    nextButton.disabled = false;
                    nextButton.classList.remove('bg-gray-300', 'cursor-not-allowed');
                    nextButton.classList.add('bg-blue-400', 'hover:bg-blue-500');
                } else {
                    nextButton.disabled = true;
                    nextButton.classList.add('bg-gray-300', 'cursor-not-allowed');
                    nextButton.classList.remove('bg-blue-400', 'hover:bg-blue-500');
                }
            }

            function showError(message) {
                // Remove existing error
                const existingError = document.querySelector('.error-message');
                if (existingError) {
                    existingError.remove();
                }

                // Create new error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4';
                errorDiv.textContent = message;

                // Insert at the top of current step
                const currentStepElement = document.getElementById(`step${currentStep}`);
                currentStepElement.insertBefore(errorDiv, currentStepElement.firstChild);
            }

            function simulateEmailSend(email) {
                // Simulate sending OTP email
                console.log(`Sending OTP to ${email}`);
                document.getElementById('emailDisplay').textContent = email;

                // Generate random 6-digit OTP for demo
                const otp = Math.floor(100000 + Math.random() * 900000);
                formData.generatedOTP = otp.toString();
                console.log(`Generated OTP: ${otp}`);

                // Show success message
                const successDiv = document.createElement('div');
                successDiv.className = 'bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4';
                successDiv.textContent = `Verification code sent! (Demo OTP: ${otp})`;

                const step4 = document.getElementById('step4');
                step4.insertBefore(successDiv, step4.firstChild);
            }

            // Step 1: Email validation
            document.getElementById('nextStep1').addEventListener('click', function() {
                const email = document.getElementById('email').value.trim();
                const terms = document.getElementById('terms').checked;

                if (!email) {
                    showError('Please enter your email address');
                    return;
                }

                if (!validateEmail(email)) {
                    showError('Please enter a valid email address');
                    return;
                }

                if (!terms) {
                    showError('You must accept the privacy policy and terms of use');
                    return;
                }

                formData.email = email;
                showStep(2);
            });

            // Step 2: Personal info
            document.getElementById('nextStep2').addEventListener('click', function() {
                const fullName = document.getElementById('fullName').value.trim();
                const businessName = document.getElementById('businessName').value.trim();

                if (!fullName) {
                    showError('Please enter your full name');
                    return;
                }

                if (!businessName) {
                    showError('Please enter your business name');
                    return;
                }

                formData.fullName = fullName;
                formData.businessName = businessName;
                showStep(3);
            });

            // Step 3: Password creation
            document.getElementById('nextStep3').addEventListener('click', function() {
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                if (!password) {
                    showError('Please enter a password');
                    return;
                }

                if (!validatePassword(password)) {
                    showError('Please ensure your password meets all requirements');
                    return;
                }

                if (password !== confirmPassword) {
                    showError('Passwords do not match');
                    return;
                }

                formData.password = password;

                // Send OTP email and move to verification step
                simulateEmailSend(formData.email);
                showStep(4);
            });

            // Step 4: Email verification
            document.getElementById('verifyOTP').addEventListener('click', function() {
                const enteredOTP = document.getElementById('otpCode').value.trim();

                if (!enteredOTP) {
                    showError('Please enter the verification code');
                    return;
                }

                if (enteredOTP !== formData.generatedOTP) {
                    showError('Invalid verification code. Please try again.');
                    return;
                }

                // Success! Create account
                this.disabled = true;
                this.innerHTML = `
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating Account...
                `;

                setTimeout(() => {
                    alert('Account created successfully! Welcome to Sentio!');
                    window.location.href = 'Testing.html';
                }, 2000);
            });

            // Resend OTP
            document.getElementById('resendOTP').addEventListener('click', function() {
                simulateEmailSend(formData.email);

                // Disable resend button temporarily
                this.disabled = true;
                this.textContent = 'Sent!';

                setTimeout(() => {
                    this.disabled = false;
                    this.textContent = 'Resend';
                }, 3000);
            });

            // Back button handlers
            document.getElementById('backStep2').addEventListener('click', () => showStep(1));
            document.getElementById('backStep3').addEventListener('click', () => showStep(2));
            document.getElementById('backStep4').addEventListener('click', () => showStep(3));

            // Password input event listeners
            document.getElementById('password').addEventListener('input', function(e) {
                updatePasswordRequirements(e.target.value);
            });

            document.getElementById('confirmPassword').addEventListener('input', function(e) {
                const password = document.getElementById('password').value;
                updatePasswordRequirements(password);
            });

            // Auto-format OTP input
            document.getElementById('otpCode').addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length > 6) value = value.substr(0, 6);
                e.target.value = value;
            });

            // Initialize first step
            showStep(1);
        });
    </script>

    <!-- External JavaScript -->
    <script src="script.js"></script>
</body>
</html>
