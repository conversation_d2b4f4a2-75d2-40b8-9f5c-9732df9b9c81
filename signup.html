<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Sentio | Launch Your E-commerce Store</title>
    <meta name="description" content="Join <PERSON> today and launch your e-commerce store in just 2 hours. Get started with our AI-powered platform for small businesses.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'brand-blue': '#3674B5',
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'dm-sans': ['DM Sans', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&family=DM+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Lordicon -->
    <script src="https://cdn.lordicon.com/lordicon.js"></script>
    
    <!-- External CSS -->
    <link rel="stylesheet" href="styles.css">
</head>

<body class="bg-gray-50 font-inter">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="Testing.html" class="text-2xl font-bold text-gray-900 font-dm-sans">
                        Sentio
                    </a>
                </div>
                
                <!-- Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="Testing.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors duration-200">Home</a>
                    <a href="Testing.html#features" class="text-gray-600 hover:text-gray-900 font-medium transition-colors duration-200">Features</a>
                    <a href="Testing.html#benefits" class="text-gray-600 hover:text-gray-900 font-medium transition-colors duration-200">Benefits</a>
                    <a href="Testing.html#pricing" class="text-gray-600 hover:text-gray-900 font-medium transition-colors duration-200">Pricing</a>
                </nav>
                
                <!-- Login Link -->
                <div class="hidden md:block">
                    <a href="#" class="text-gray-600 hover:text-gray-900 font-medium transition-colors duration-200 mr-4">Login</a>
                    <span class="text-brand-blue font-semibold">Sign Up</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header Section -->
            <div class="text-center">
                <div class="mx-auto h-12 w-12 bg-brand-blue rounded-full flex items-center justify-center mb-6">
                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 font-dm-sans">
                    Launch Your Store Today
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                    Join thousands of entrepreneurs who trust Sentio to power their e-commerce success
                </p>
            </div>

            <!-- Sign Up Form -->
            <form class="mt-8 space-y-6" id="signupForm">
                <div class="space-y-4">
                    <!-- Full Name -->
                    <div>
                        <label for="fullName" class="block text-sm font-medium text-gray-700 mb-1">
                            Full Name
                        </label>
                        <input 
                            id="fullName" 
                            name="fullName" 
                            type="text" 
                            required 
                            class="appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue focus:z-10 sm:text-sm transition-all duration-200"
                            placeholder="Enter your full name"
                        >
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                            Email Address
                        </label>
                        <input 
                            id="email" 
                            name="email" 
                            type="email" 
                            required 
                            class="appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue focus:z-10 sm:text-sm transition-all duration-200"
                            placeholder="Enter your email address"
                        >
                    </div>

                    <!-- Business Name -->
                    <div>
                        <label for="businessName" class="block text-sm font-medium text-gray-700 mb-1">
                            Business Name
                        </label>
                        <input 
                            id="businessName" 
                            name="businessName" 
                            type="text" 
                            required 
                            class="appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue focus:z-10 sm:text-sm transition-all duration-200"
                            placeholder="Enter your business name"
                        >
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                            Password
                        </label>
                        <input 
                            id="password" 
                            name="password" 
                            type="password" 
                            required 
                            class="appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue focus:z-10 sm:text-sm transition-all duration-200"
                            placeholder="Create a secure password"
                        >
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">
                            Confirm Password
                        </label>
                        <input 
                            id="confirmPassword" 
                            name="confirmPassword" 
                            type="password" 
                            required 
                            class="appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue focus:border-brand-blue focus:z-10 sm:text-sm transition-all duration-200"
                            placeholder="Confirm your password"
                        >
                    </div>
                </div>

                <!-- Terms and Privacy -->
                <div class="flex items-start">
                    <div class="flex items-center h-5">
                        <input 
                            id="terms" 
                            name="terms" 
                            type="checkbox" 
                            required
                            class="focus:ring-brand-blue h-4 w-4 text-brand-blue border-gray-300 rounded"
                        >
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="terms" class="text-gray-600">
                            I agree to the 
                            <a href="#" class="text-brand-blue hover:text-blue-700 font-medium">Terms of Service</a> 
                            and 
                            <a href="#" class="text-brand-blue hover:text-blue-700 font-medium">Privacy Policy</a>
                        </label>
                    </div>
                </div>

                <!-- Submit Button -->
                <div>
                    <button 
                        type="submit" 
                        class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-brand-blue hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-blue transition-all duration-300 transform hover:scale-105"
                    >
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <lord-icon
                                src="https://cdn.lordicon.com/jgnvfzqg.json"
                                trigger="hover"
                                colors="primary:#ffffff"
                                style="width:20px;height:20px">
                            </lord-icon>
                        </span>
                        Create Your Store
                    </button>
                </div>

                <!-- Login Link -->
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Already have an account? 
                        <a href="#" class="font-medium text-brand-blue hover:text-blue-700 transition-colors duration-200">
                            Sign in here
                        </a>
                    </p>
                </div>
            </form>
        </div>
    </main>

    <!-- Features Preview -->
    <section class="bg-white py-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h3 class="text-2xl font-bold text-gray-900 font-dm-sans mb-4">
                    What You'll Get With Sentio
                </h3>
                <p class="text-gray-600">
                    Everything you need to launch and grow your e-commerce business
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="text-center">
                    <div class="mx-auto h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                        <lord-icon
                            src="https://cdn.lordicon.com/qhviklyi.json"
                            trigger="hover"
                            colors="primary:#3674B5"
                            style="width:24px;height:24px">
                        </lord-icon>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">2-Hour Setup</h4>
                    <p class="text-gray-600 text-sm">Get your store live and ready to sell in just 2 hours with our guided setup process.</p>
                </div>
                
                <!-- Feature 2 -->
                <div class="text-center">
                    <div class="mx-auto h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                        <lord-icon
                            src="https://cdn.lordicon.com/wxnxiano.json"
                            trigger="hover"
                            colors="primary:#3674B5"
                            style="width:24px;height:24px">
                        </lord-icon>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">AI-Powered Analytics</h4>
                    <p class="text-gray-600 text-sm">Smart insights and predictive analytics to help you make data-driven decisions.</p>
                </div>
                
                <!-- Feature 3 -->
                <div class="text-center">
                    <div class="mx-auto h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                        <lord-icon
                            src="https://cdn.lordicon.com/kiynvdns.json"
                            trigger="hover"
                            colors="primary:#3674B5"
                            style="width:24px;height:24px">
                        </lord-icon>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">24/7 Support</h4>
                    <p class="text-gray-600 text-sm">Round-the-clock customer support to help you succeed every step of the way.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Signup Form JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const signupForm = document.getElementById('signupForm');
            const submitButton = signupForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;

            // Form validation
            function validateForm() {
                const fullName = document.getElementById('fullName').value.trim();
                const email = document.getElementById('email').value.trim();
                const businessName = document.getElementById('businessName').value.trim();
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                const terms = document.getElementById('terms').checked;

                // Reset previous error states
                clearErrors();

                let isValid = true;
                const errors = [];

                // Validate full name
                if (fullName.length < 2) {
                    showFieldError('fullName', 'Please enter your full name');
                    errors.push('Full name is required');
                    isValid = false;
                }

                // Validate email
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    showFieldError('email', 'Please enter a valid email address');
                    errors.push('Valid email is required');
                    isValid = false;
                }

                // Validate business name
                if (businessName.length < 2) {
                    showFieldError('businessName', 'Please enter your business name');
                    errors.push('Business name is required');
                    isValid = false;
                }

                // Validate password
                if (password.length < 8) {
                    showFieldError('password', 'Password must be at least 8 characters long');
                    errors.push('Password must be at least 8 characters');
                    isValid = false;
                }

                // Validate password confirmation
                if (password !== confirmPassword) {
                    showFieldError('confirmPassword', 'Passwords do not match');
                    errors.push('Passwords must match');
                    isValid = false;
                }

                // Validate terms acceptance
                if (!terms) {
                    showFieldError('terms', 'You must agree to the Terms of Service and Privacy Policy');
                    errors.push('Terms acceptance is required');
                    isValid = false;
                }

                return isValid;
            }

            // Show field error
            function showFieldError(fieldId, message) {
                const field = document.getElementById(fieldId);
                field.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
                field.classList.remove('border-gray-300', 'focus:ring-brand-blue', 'focus:border-brand-blue');

                // Add error message if it doesn't exist
                let errorMsg = field.parentNode.querySelector('.error-message');
                if (!errorMsg) {
                    errorMsg = document.createElement('p');
                    errorMsg.className = 'error-message text-red-500 text-xs mt-1';
                    field.parentNode.appendChild(errorMsg);
                }
                errorMsg.textContent = message;
            }

            // Clear all errors
            function clearErrors() {
                const fields = ['fullName', 'email', 'businessName', 'password', 'confirmPassword'];
                fields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
                    field.classList.add('border-gray-300', 'focus:ring-brand-blue', 'focus:border-brand-blue');

                    const errorMsg = field.parentNode.querySelector('.error-message');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                });

                // Clear terms error
                const termsError = document.getElementById('terms').parentNode.parentNode.querySelector('.error-message');
                if (termsError) {
                    termsError.remove();
                }
            }

            // Handle form submission
            signupForm.addEventListener('submit', function(e) {
                e.preventDefault();

                if (!validateForm()) {
                    return;
                }

                // Show loading state
                submitButton.disabled = true;
                submitButton.innerHTML = `
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </span>
                    Creating Your Account...
                `;

                // Simulate API call
                setTimeout(() => {
                    // Success state
                    submitButton.innerHTML = `
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </span>
                        Account Created Successfully!
                    `;
                    submitButton.classList.remove('bg-brand-blue', 'hover:bg-blue-700');
                    submitButton.classList.add('bg-green-600');

                    // Show success message
                    setTimeout(() => {
                        alert('Welcome to Sentio! Your account has been created successfully. You will be redirected to your dashboard shortly.');
                        // In a real application, you would redirect to the dashboard
                        // window.location.href = 'dashboard.html';
                    }, 1000);

                }, 2000);
            });

            // Real-time validation feedback
            const fields = ['fullName', 'email', 'businessName', 'password', 'confirmPassword'];
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                field.addEventListener('blur', function() {
                    // Clear previous error for this field
                    field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
                    field.classList.add('border-gray-300', 'focus:ring-brand-blue', 'focus:border-brand-blue');

                    const errorMsg = field.parentNode.querySelector('.error-message');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                });
            });
        });
    </script>

    <!-- External JavaScript -->
    <script src="script.js"></script>
</body>
</html>
